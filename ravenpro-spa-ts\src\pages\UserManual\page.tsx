import { useState, useMemo, useEffect } from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Table,
    Form,
    Row,
    Col,
    InputGroup,
    Alert,
    Card,
    Breadcrumb,
    Ta<PERSON>,
    Tab,
    Spinner
} from "react-bootstrap";
import { PiNotebook, PiTrash, PiPlus, PiMagnifyingGlass } from "react-icons/pi";
import {
    useGetDocumentsQuery,
    useCreateDocumentMutation,
    useDeleteDocumentMutation,
    DocumentStatusFilter,
    type Document,
    type DocumentStatus,
    useGetDocumentsLazyQuery,
    type GetDocumentsQuery,
} from "../../gql/graphql";
import { StatusPill } from "./components/status-pill";
import CreateModal from "./components/create-modal";
import DeleteModal from "./components/delete-modal";

type documentFilter = {
    mineOnly?: boolean;
    first?: string;
    last?: string;
    status?: DocumentStatusFilter;
    tag?: string;
}

function UserManualPage() {
    const [createDocument] = useCreateDocumentMutation();
    const [deleteDocument] = useDeleteDocumentMutation();

    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [selectedManual, setSelectedManual] = useState<Document | null>(null);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);

    const [searchKey, setSearchKey] = useState<string>('status');
    const [filter, setFilter] = useState<documentFilter>({
        mineOnly: true,
        last: new Date().toISOString(),
        first: new Date(new Date().setMonth(new Date().getMonth() - 6)).toISOString(),
        status: DocumentStatusFilter.All,
        tag: ''
    });

    const TOKEN = 100;
    const TOKENLIMITMAX = 1000;

    // GraphQL Snapshot
    const [fetchDocs, { loading: docsLoading, error: docsError, data: docsData, refetch: docsRefetch }] = useGetDocumentsLazyQuery();

    const loadDocs = ((token: string | null = null) => {
        fetchDocs({
            variables: {
                token,
                limit: TOKENLIMITMAX,
                mineOnly: filter.mineOnly!,
                first: filter.first,
                last: filter.last,
                status: filter.status!
            },
            fetchPolicy: 'network-only'
        })
    });

    // Initial Load
    useEffect(()=>{
        loadDocs();
    }, [])

    // Utilities
    function parseDocs(docsData: GetDocumentsQuery): Document[] {
        return docsData.documents?.items
    };

    const handleDelete = ((document: Document) => {
        setSelectedManual(document);
        setShowDeleteModal(true);
    })

    function toReadableLocalTime(isoString: string): string {

        try {
            const date = new Date(isoString);

            // Format date as DD/MM/YYYY
            const datePart = date.toLocaleDateString(undefined, {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
            });

            // Format time as hh:mm am/pm
            const timePart = date.toLocaleTimeString(undefined, {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
            }).toLowerCase();

            return `${datePart} ${timePart}`;
        } catch {
            return "Nil"
        }
    }

    // Button Funcs
    const handleSearch = (() => {
        if (searchKey === "status") {
            loadDocs();
        }
    });


    return (
        <Container className="mt-4">
            {/* Header */}
            <Breadcrumb>
                <Breadcrumb.Item href="/">Home</Breadcrumb.Item>
                <Breadcrumb.Item active>UserManualProcessing</Breadcrumb.Item>
            </Breadcrumb>
            <Stack direction="horizontal" gap={2}>
                <div className="me-auto">
                    <h3><PiNotebook /> User Manual Preprocessing</h3>
                    <p className="text-muted">Upload your user manual to be preprocessed for verification.</p>
                </div>
                <div className="ms-auto">
                    <Button variant="dark" onClick={() => setShowCreateModal(true)}>
                        <PiPlus /> New
                    </Button>
                </div>
            </Stack>

            <Card className="mb-3">
                <Card.Body>
                    <Row>
                        <Col md={8}>
                            <Tabs id="search-panel" activeKey={searchKey} onSelect={(k) => setSearchKey(k!)} className="mb-3">
                                <Tab eventKey="Search" title="Search By: " disabled />
                                <Tab eventKey="status" title="Status">
                                    <InputGroup className="p-2">
                                        <InputGroup.Text>Status</InputGroup.Text>
                                        <Form.Select
                                            value={filter.status}
                                            onChange={(e) =>
                                                setFilter(prev => ({
                                                    ...prev,
                                                    status: e.target.value as DocumentStatusFilter
                                                }))
                                            }>
                                            {Object.values(DocumentStatusFilter).map((status) => (
                                                <option key={status} value={status}>
                                                    {status}
                                                </option>
                                            ))}
                                        </Form.Select>
                                        <InputGroup.Text>Mine Only</InputGroup.Text>
                                        <InputGroup.Text>
                                            <Form.Check type="checkbox"
                                                checked={filter.mineOnly}
                                                onChange={(e) =>
                                                    setFilter(prev => ({
                                                        ...prev,
                                                        mineOnly: e.target.checked
                                                    }))} />
                                        </InputGroup.Text>
                                    </InputGroup>
                                </Tab>
                            </Tabs>
                        </Col>
                        <Col md={4}>
                            <InputGroup className="p-2">
                                <InputGroup.Text>From</InputGroup.Text>
                                <Form.Control
                                    type="date"
                                    value={filter.first!.slice(0, 10)}
                                    onKeyDown={(e) => e.preventDefault()}
                                    onChange={(e) =>
                                        setFilter(prev => ({
                                            ...prev,
                                            first: new Date(e.target.value).toISOString() || ""
                                        }))} />
                            </InputGroup>
                            <InputGroup className="p-2">
                                <InputGroup.Text>To</InputGroup.Text>
                                <Form.Control
                                    type="date"
                                    value={filter.last!.slice(0, 10)}
                                    onKeyDown={(e) => e.preventDefault()}
                                    onChange={(e) =>
                                        setFilter(prev => ({
                                            ...prev,
                                            last: new Date(e.target.value).toISOString() || ""
                                        }))} />
                            </InputGroup>
                        </Col>
                    </Row>
                    <div className="d-flex">
                        <Button variant="outline-dark" className="ms-auto" onClick={handleSearch}>Search</Button>
                    </div>
                </Card.Body>
            </Card>

            {/* Main Table */}
            <Card>
                <Card.Body>
                    <Row>
                        <Col>
                            <InputGroup className="mb-4">
                                <InputGroup.Text><PiMagnifyingGlass></PiMagnifyingGlass></InputGroup.Text>
                                <Form.Control type="text" placeholder="Filter By.. ">
                                    
                                </Form.Control>
                            </InputGroup>
                        </Col>
                    </Row>

                    <Table hover responsive bordered className="small-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Version</th>
                                <th>Updated By</th>
                                <th>Start</th>
                                <th>End</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {docsLoading ?
                                <tr><td colSpan={7}><Spinner animation="border" /></td></tr>
                                : docsError ?
                                    <tr><td colSpan={7}><Alert variant="danger">{docsError.message}</Alert></td></tr>
                                    : docsData ?
                                        <>
                                            {parseDocs(docsData).map((key) => (
                                                <tr key={key.documentId! + key.version!}>
                                                    <td>{key.documentId}</td>
                                                    <td>{key.version}</td>
                                                    <td>{key.updatedBy}</td>
                                                    <td>{toReadableLocalTime(key.startTime)}</td>
                                                    <td>{key.endTime ? toReadableLocalTime(key.endTime.value) : "-"}</td>
                                                    <td><StatusPill status={key.status!} /></td>
                                                    <td>
                                                        <Button variant="outline-danger" size="sm" onClick={()=>handleDelete(key!)}><PiTrash></PiTrash></Button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </> :
                                        <>
                                            <tr><td colSpan={7}><Alert variant="light">Data not retrieved</Alert></td></tr>
                                        </>
                            }
                        </tbody>
                    </Table>
                </Card.Body>
            </Card>

        <CreateModal open={showCreateModal} handleRefetch={loadDocs} onClose={setShowCreateModal} />
        <DeleteModal open={showDeleteModal} handleRefetch={loadDocs} onClose={setShowDeleteModal} document={selectedManual!} />
        
        </Container>

    );
}

export default UserManualPage;