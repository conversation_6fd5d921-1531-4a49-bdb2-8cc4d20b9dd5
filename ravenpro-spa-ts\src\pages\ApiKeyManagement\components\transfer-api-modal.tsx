

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Form, Modal, Toast } from "react-bootstrap";
import { useTransferApiKeyMutation, type ApiK<PERSON> } from "../../../gql/graphql";
import { PiWarning } from "react-icons/pi";

interface TransferApiKeyModalProps {
    open: boolean;
    onClose: (state: boolean) => void;
    handleKeysRefetch: () => void;
    apiKey: ApiKey
}

export default function TransferApiKeyModal({ open, onClose, handleKeysRefetch, apiKey }: TransferApiKeyModalProps) {

    const [showToast, setShowToast] = useState<boolean>(false);
    const [toastMessage, setToastMessage] = useState<string>("");
    const [email, setEmail] = useState<string>("");
    const [transferApiKey, { loading }] = useTransferApiKeyMutation();

    const handleTransferApiKey = (async () => {
        try {
            await transferApiKey({
                variables: {
                    sha: apiKey.id!,
                    email: email
                }
            });
            setToastMessage(`ApiKey ${apiKey.name} transfered to ${email} successfully!`);
            handleCloseModal();
            setShowToast(true);
            handleKeysRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const handleCloseModal = (() => {
        onClose(false);
    });

    return (
        <>
            <Modal show={open} onHide={handleCloseModal} centered>
                <Modal.Header closeButton>
                    <div>
                        <h5 className="mb-0">Transfer API Key</h5>
                    </div>
                </Modal.Header>
                <Modal.Body>
                    <Alert variant="warning">
                        <b><PiWarning></PiWarning> Warning! </b>Please ensure the email is correct. Mistakes can result in key loss.
                    </Alert>
                    <Form.Group className="mb-2">
                        <Form.Label><h6>Transfer APIKey [{apiKey?.name}] to: </h6></Form.Label>
                        <Form.Control
                            required
                            type="email"
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder="<EMAIL>"
                        />
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="light" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button onClick={handleTransferApiKey} variant="dark" disabled={loading || !email.trim()}>
                        {loading ? "Transfering key.." : "Transfer API Key"}
                    </Button>
                </Modal.Footer>
            </Modal>
            <Toast
                onClose={() => setShowToast(false)}
                show={showToast}
                delay={5000}
                autohide
                style={{
                    position: 'fixed',
                    bottom: 20,
                    right: 20,
                    minWidth: '200px',
                }}
            >
                <Toast.Header>
                    <strong className="me-auto">Notification</strong>
                </Toast.Header>
                <Toast.Body>{toastMessage}</Toast.Body>
            </Toast>

        </>
    )

}




