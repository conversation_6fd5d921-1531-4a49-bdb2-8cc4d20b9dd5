

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal, Toast } from "react-bootstrap";
import { useDeleteRequirementSnapshotMutation, type RequirementSnapshot } from "../../../gql/graphql";
import { PiWarning } from "react-icons/pi";

interface DeleteModalProps {
    open: boolean;
    onClose: (state: boolean) => void;
    handleRefetch: () => void;
    snapshot: RequirementSnapshot
}

export default function DeleteModal({ open, onClose, handleRefetch, snapshot }: DeleteModalProps) {

    const [showToast, setShowToast] = useState<boolean>(false);
    const [toastMessage, setToastMessage] = useState<string>("");
    const [deleteSnapshot, { loading }] = useDeleteRequirementSnapshotMutation();

    const handleDeleteSnapshot = (async () => {
        try {
            await deleteSnapshot({
                variables: {
                    snapshotId: snapshot.id
                }
            });
            setToastMessage(`Snapshot ${snapshot.id} deleted successfully!`);
            handleCloseModal();
            setShowToast(true);
            handleRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const handleCloseModal = (() => {
        onClose(false);
    });

    return (
        <>
            <Modal show={open} onHide={handleCloseModal} centered>
                <Modal.Header closeButton>
                    <div>
                        <h5 className="mb-0">Delete Snapshot</h5>
                    </div>
                </Modal.Header>
                <Modal.Body>
                    <Alert variant="danger">
                        <b><PiWarning></PiWarning> Warning! </b>You are about to delete the following Snapshot: <br />
                        <ul>
                            <li><b>ID: </b>{snapshot?.id}<br /></li>
                        </ul>
                        This action is irreversible. Deleting this snapshot may result in:
                        <ul>
                            <li>Loss of access to associated services or applications</li>
                            <li>Service disruptions or downtime</li>
                            <li>Security audit logs being impacted</li>
                            <li>Dependency failures if this snapshot is in active use</li>
                        </ul>
                    </Alert>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="light" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button onClick={handleDeleteSnapshot} variant="danger" disabled={loading}>
                        {loading ? "Deleting.." : "Delete Snapshot"}
                    </Button>
                </Modal.Footer>
            </Modal>
            <Toast
                onClose={() => setShowToast(false)}
                show={showToast}
                delay={5000}
                autohide
                style={{
                    position: 'fixed',
                    bottom: 20,
                    right: 20,
                    minWidth: '200px',
                }}
            >
                <Toast.Header>
                    <strong className="me-auto">Notification</strong>
                </Toast.Header>
                <Toast.Body>{toastMessage}</Toast.Body>
            </Toast>

        </>
    )

}




