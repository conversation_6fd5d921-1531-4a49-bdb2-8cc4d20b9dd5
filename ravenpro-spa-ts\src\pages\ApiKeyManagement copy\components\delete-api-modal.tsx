

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal, Toast } from "react-bootstrap";
import { useDeleteApiKeyMutation, type ApiK<PERSON> } from "../../../gql/graphql";
import { PiWarning } from "react-icons/pi";

interface DeleteApiKeyModalProps {
    open: boolean;
    onClose: (state: boolean) => void;
    handleKeysRefetch: () => void;
    apiKey: ApiKey
}

export default function DeleteApiKeyModal({ open, onClose, handleKeysRefetch, apiKey }: DeleteApiKeyModalProps) {

    const [showToast, setShowToast] = useState<boolean>(false);
    const [toastMessage, setToastMessage] = useState<string>("");
    const [deleteApiKey, { loading }] = useDeleteApiKeyMutation();


    const handleDeleteApiKey = (async () => {
        try {
            await deleteApiKey({
                variables: {
                    sha: apiKey.id!
                }
            });
            setToastMessage(`ApiKey ${apiKey.name} deleted successfully!`);
            handleCloseModal();
            setShowToast(true);
            handleKeysRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const handleCloseModal = (() => {
        onClose(false);
    });

    return (
        <>
            <Modal show={open} onHide={handleCloseModal} centered>
                <Modal.Header closeButton>
                    <div>
                        <h5 className="mb-0">Delete API Key</h5>
                    </div>
                </Modal.Header>
                <Modal.Body>
                    <Alert variant="danger">
                        <b><PiWarning></PiWarning> Warning! </b>You are about to delete the following key: <br />
                        <ul>
                            <li><b>Key Name: </b>{apiKey?.name}<br /></li>
                        </ul>
                        This action is irreversible. Deleting this key may result in:
                        <ul>
                            <li>Loss of access to associated services or applications</li>
                            <li>Service disruptions or downtime</li>
                            <li>Security audit logs being impacted</li>
                            <li>Dependency failures if this key is in active use</li>
                        </ul>
                    </Alert>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="light" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button onClick={handleDeleteApiKey} variant="danger" disabled={loading}>
                        {loading ? "Deleting key.." : "Delete API Key"}
                    </Button>
                </Modal.Footer>
            </Modal>
            <Toast
                onClose={() => setShowToast(false)}
                show={showToast}
                delay={5000}
                autohide
                style={{
                    position: 'fixed',
                    bottom: 20,
                    right: 20,
                    minWidth: '200px',
                }}
            >
                <Toast.Header>
                    <strong className="me-auto">Notification</strong>
                </Toast.Header>
                <Toast.Body>{toastMessage}</Toast.Body>
            </Toast>

        </>
    )

}




