import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** The `DateTime` scalar represents an ISO-8601 compliant date time type. */
  DateTime: { input: any; output: any; }
};

export type ApiKey = {
  __typename?: 'APIKey';
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  owner?: Maybe<Scalars['String']['output']>;
  status: ApiKeyStatus;
};

export enum ApiKeyStatus {
  Active = 'ACTIVE',
  Deleting = 'DELETING',
  Unknown = 'UNKNOWN'
}

export type ApiKeyAccessRight = {
  __typename?: 'ApiKeyAccessRight';
  accessorEmail?: Maybe<Scalars['String']['output']>;
  keyID?: Maybe<Scalars['String']['output']>;
};

export type ApiKeyRecord = {
  __typename?: 'ApiKeyRecord';
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  owner?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type Document = {
  __typename?: 'Document';
  documentId?: Maybe<Scalars['String']['output']>;
  endTime?: Maybe<FSharpOptionOfDateTime>;
  startTime: Scalars['DateTime']['output'];
  status?: Maybe<DocumentStatus>;
  updatedBy?: Maybe<Scalars['String']['output']>;
  uploadUrl?: Maybe<FSharpOptionOfString>;
  version?: Maybe<Scalars['String']['output']>;
};

export type DocumentStatus = DocumentStatusAnnotating | DocumentStatusComplete | DocumentStatusEmbedding | DocumentStatusError | DocumentStatusSplitting | DocumentStatusStarted | DocumentStatusUploaded;

export type DocumentStatusAnnotating = {
  __typename?: 'DocumentStatusAnnotating';
  current: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type DocumentStatusComplete = {
  __typename?: 'DocumentStatusComplete';
  status?: Maybe<Scalars['String']['output']>;
};

export type DocumentStatusEmbedding = {
  __typename?: 'DocumentStatusEmbedding';
  current: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type DocumentStatusError = {
  __typename?: 'DocumentStatusError';
  errorMessage?: Maybe<Scalars['String']['output']>;
};

export enum DocumentStatusFilter {
  All = 'ALL',
  Complete = 'COMPLETE',
  Error = 'ERROR',
  Running = 'RUNNING'
}

export type DocumentStatusSplitting = {
  __typename?: 'DocumentStatusSplitting';
  status?: Maybe<Scalars['String']['output']>;
};

export type DocumentStatusStarted = {
  __typename?: 'DocumentStatusStarted';
  status?: Maybe<Scalars['String']['output']>;
};

export type DocumentStatusUploaded = {
  __typename?: 'DocumentStatusUploaded';
  status?: Maybe<Scalars['String']['output']>;
};

export type FSharpOptionOfDateTime = {
  __typename?: 'FSharpOptionOfDateTime';
  get_Value: Scalars['DateTime']['output'];
  value: Scalars['DateTime']['output'];
};

export type FSharpOptionOfString = {
  __typename?: 'FSharpOptionOfString';
  get_Value?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  createApiKey?: Maybe<ApiKey>;
  createDocument?: Maybe<Document>;
  createRequirementTag?: Maybe<RequirementSnapshotTag>;
  createVerification?: Maybe<Verification>;
  createVerificationTag?: Maybe<VerificationTag>;
  deleteApiKey: Scalars['Boolean']['output'];
  deleteDocument: Scalars['Boolean']['output'];
  deleteRequirementSnapshot: Scalars['Boolean']['output'];
  deleteRequirementTag: Scalars['Boolean']['output'];
  deleteVerification: Scalars['Boolean']['output'];
  deleteVerificationTag: Scalars['Boolean']['output'];
  importRequirement?: Maybe<RequirementSnapshot>;
  renameApiKey?: Maybe<ApiKeyRecord>;
  renameRequirementTag: Scalars['Boolean']['output'];
  renameVerificationTag: Scalars['Boolean']['output'];
  revokeApiKey: Scalars['Boolean']['output'];
  shareApiKey?: Maybe<ApiKeyAccessRight>;
  tagRequirementSnapshot: Scalars['Boolean']['output'];
  tagVerification: Scalars['Boolean']['output'];
  transferApiKey: Scalars['Boolean']['output'];
  untagRequirementSnapshot: Scalars['Boolean']['output'];
  untagVerification: Scalars['Boolean']['output'];
};


export type MutationCreateApiKeyArgs = {
  name?: InputMaybe<Scalars['String']['input']>;
  secret?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateDocumentArgs = {
  apiKeyId?: InputMaybe<Scalars['String']['input']>;
  documentId?: InputMaybe<Scalars['String']['input']>;
  version?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateRequirementTagArgs = {
  text?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateVerificationArgs = {
  apiKeyId?: InputMaybe<Scalars['String']['input']>;
  documentId?: InputMaybe<Scalars['String']['input']>;
  documentVersion?: InputMaybe<Scalars['String']['input']>;
  snapshotId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCreateVerificationTagArgs = {
  text?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteApiKeyArgs = {
  sha?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteDocumentArgs = {
  documentId?: InputMaybe<Scalars['String']['input']>;
  version?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteRequirementSnapshotArgs = {
  snapshotId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteRequirementTagArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
  text?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteVerificationArgs = {
  verificationId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteVerificationTagArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
  text?: InputMaybe<Scalars['String']['input']>;
};


export type MutationImportRequirementArgs = {
  apiKeyId?: InputMaybe<Scalars['String']['input']>;
  jamaIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};


export type MutationRenameApiKeyArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  sha?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRenameRequirementTagArgs = {
  newText?: InputMaybe<Scalars['String']['input']>;
  oldText?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRenameVerificationTagArgs = {
  newText?: InputMaybe<Scalars['String']['input']>;
  oldText?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRevokeApiKeyArgs = {
  email?: InputMaybe<Scalars['String']['input']>;
  sha?: InputMaybe<Scalars['String']['input']>;
};


export type MutationShareApiKeyArgs = {
  email?: InputMaybe<Scalars['String']['input']>;
  sha?: InputMaybe<Scalars['String']['input']>;
};


export type MutationTagRequirementSnapshotArgs = {
  snapshotId?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
  tagText?: InputMaybe<Scalars['String']['input']>;
};


export type MutationTagVerificationArgs = {
  tagId?: InputMaybe<Scalars['String']['input']>;
  tagText?: InputMaybe<Scalars['String']['input']>;
  verificationId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationTransferApiKeyArgs = {
  email?: InputMaybe<Scalars['String']['input']>;
  sha?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUntagRequirementSnapshotArgs = {
  snapshotId?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUntagVerificationArgs = {
  tagId?: InputMaybe<Scalars['String']['input']>;
  verificationId?: InputMaybe<Scalars['String']['input']>;
};

export type PaginatedOfApiKey = {
  __typename?: 'PaginatedOfAPIKey';
  items?: Maybe<Array<Maybe<ApiKey>>>;
  nextToken?: Maybe<Scalars['String']['output']>;
};

export type PaginatedOfApiKeyAccessRight = {
  __typename?: 'PaginatedOfApiKeyAccessRight';
  items?: Maybe<Array<Maybe<ApiKeyAccessRight>>>;
  nextToken?: Maybe<Scalars['String']['output']>;
};

export type PaginatedOfDocument = {
  __typename?: 'PaginatedOfDocument';
  items?: Maybe<Array<Maybe<Document>>>;
  nextToken?: Maybe<Scalars['String']['output']>;
};

export type PaginatedOfRequirementSnapshot = {
  __typename?: 'PaginatedOfRequirementSnapshot';
  items?: Maybe<Array<Maybe<RequirementSnapshot>>>;
  nextToken?: Maybe<Scalars['String']['output']>;
};

export type PaginatedOfRequirementSnapshotTag = {
  __typename?: 'PaginatedOfRequirementSnapshotTag';
  items?: Maybe<Array<Maybe<RequirementSnapshotTag>>>;
  nextToken?: Maybe<Scalars['String']['output']>;
};

export type PaginatedOfVerification = {
  __typename?: 'PaginatedOfVerification';
  items?: Maybe<Array<Maybe<Verification>>>;
  nextToken?: Maybe<Scalars['String']['output']>;
};

export type PaginatedOfVerificationTag = {
  __typename?: 'PaginatedOfVerificationTag';
  items?: Maybe<Array<Maybe<VerificationTag>>>;
  nextToken?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  accessibleKeys?: Maybe<PaginatedOfApiKey>;
  accessors?: Maybe<PaginatedOfApiKeyAccessRight>;
  apiKey?: Maybe<ApiKey>;
  document?: Maybe<Document>;
  documents?: Maybe<PaginatedOfDocument>;
  ownedKeys?: Maybe<PaginatedOfApiKey>;
  requirementSnapshot?: Maybe<RequirementSnapshot>;
  requirementSnapshots?: Maybe<PaginatedOfRequirementSnapshot>;
  requirementTag?: Maybe<RequirementSnapshotTag>;
  requirementTags?: Maybe<PaginatedOfRequirementSnapshotTag>;
  secret?: Maybe<Scalars['String']['output']>;
  snapshotsWithTag?: Maybe<PaginatedOfRequirementSnapshot>;
  verification?: Maybe<Verification>;
  verificationReportUrl?: Maybe<Scalars['String']['output']>;
  verificationTag?: Maybe<VerificationTag>;
  verificationTags?: Maybe<PaginatedOfVerificationTag>;
  verifications?: Maybe<PaginatedOfVerification>;
  verificationsWithTag?: Maybe<PaginatedOfVerification>;
};


export type QueryAccessibleKeysArgs = {
  limit: Scalars['Int']['input'];
  token?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAccessorsArgs = {
  limit: Scalars['Int']['input'];
  sha?: InputMaybe<Scalars['String']['input']>;
  token?: InputMaybe<Scalars['String']['input']>;
};


export type QueryApiKeyArgs = {
  sha?: InputMaybe<Scalars['String']['input']>;
};


export type QueryDocumentArgs = {
  documentId?: InputMaybe<Scalars['String']['input']>;
  version?: InputMaybe<Scalars['String']['input']>;
};


export type QueryDocumentsArgs = {
  first: Scalars['DateTime']['input'];
  last: Scalars['DateTime']['input'];
  limit: Scalars['Int']['input'];
  mineOnly: Scalars['Boolean']['input'];
  status: DocumentStatusFilter;
  token?: InputMaybe<Scalars['String']['input']>;
};


export type QueryOwnedKeysArgs = {
  limit: Scalars['Int']['input'];
  token?: InputMaybe<Scalars['String']['input']>;
};


export type QueryRequirementSnapshotArgs = {
  snapshotId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryRequirementSnapshotsArgs = {
  first: Scalars['DateTime']['input'];
  last: Scalars['DateTime']['input'];
  limit: Scalars['Int']['input'];
  mineOnly: Scalars['Boolean']['input'];
  statusFilter: RequirementSnapshotStatusFilter;
  token?: InputMaybe<Scalars['String']['input']>;
};


export type QueryRequirementTagArgs = {
  tagId?: InputMaybe<Scalars['String']['input']>;
  text?: InputMaybe<Scalars['String']['input']>;
};


export type QueryRequirementTagsArgs = {
  limit: Scalars['Int']['input'];
  prefix?: InputMaybe<Scalars['String']['input']>;
  token?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySecretArgs = {
  sha?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySnapshotsWithTagArgs = {
  first: Scalars['DateTime']['input'];
  last: Scalars['DateTime']['input'];
  limit: Scalars['Int']['input'];
  tag?: InputMaybe<Scalars['String']['input']>;
  token?: InputMaybe<Scalars['String']['input']>;
};


export type QueryVerificationArgs = {
  verificationId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryVerificationReportUrlArgs = {
  verificationId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryVerificationTagArgs = {
  tagId?: InputMaybe<Scalars['String']['input']>;
  text?: InputMaybe<Scalars['String']['input']>;
};


export type QueryVerificationTagsArgs = {
  limit: Scalars['Int']['input'];
  prefix?: InputMaybe<Scalars['String']['input']>;
  token?: InputMaybe<Scalars['String']['input']>;
};


export type QueryVerificationsArgs = {
  first: Scalars['DateTime']['input'];
  last: Scalars['DateTime']['input'];
  limit: Scalars['Int']['input'];
  mineOnly: Scalars['Boolean']['input'];
  statusFilter: VerificationStatusFilter;
  token?: InputMaybe<Scalars['String']['input']>;
};


export type QueryVerificationsWithTagArgs = {
  first: Scalars['DateTime']['input'];
  last: Scalars['DateTime']['input'];
  limit: Scalars['Int']['input'];
  tag?: InputMaybe<Scalars['String']['input']>;
  token?: InputMaybe<Scalars['String']['input']>;
};

export type RequirementSnapshot = {
  __typename?: 'RequirementSnapshot';
  author?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  downloadUrl?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  status?: Maybe<RequirementSnapshotStatus>;
  tags?: Maybe<Array<Maybe<RequirementSnapshotTag>>>;
};

export type RequirementSnapshotStatus = RequirementSnapshotStatusDeleting | RequirementSnapshotStatusDone | RequirementSnapshotStatusError | RequirementSnapshotStatusFetching | RequirementSnapshotStatusProcessing | RequirementSnapshotStatusStarting;

export type RequirementSnapshotStatusDeleting = {
  __typename?: 'RequirementSnapshotStatusDeleting';
  status?: Maybe<Scalars['String']['output']>;
};

export type RequirementSnapshotStatusDone = {
  __typename?: 'RequirementSnapshotStatusDone';
  status?: Maybe<Scalars['String']['output']>;
};

export type RequirementSnapshotStatusError = {
  __typename?: 'RequirementSnapshotStatusError';
  errorMessage?: Maybe<Scalars['String']['output']>;
};

export type RequirementSnapshotStatusFetching = {
  __typename?: 'RequirementSnapshotStatusFetching';
  completed: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export enum RequirementSnapshotStatusFilter {
  All = 'ALL',
  Completed = 'COMPLETED',
  Failed = 'FAILED',
  Running = 'RUNNING'
}

export type RequirementSnapshotStatusProcessing = {
  __typename?: 'RequirementSnapshotStatusProcessing';
  completed: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type RequirementSnapshotStatusStarting = {
  __typename?: 'RequirementSnapshotStatusStarting';
  status?: Maybe<Scalars['String']['output']>;
};

export type RequirementSnapshotTag = {
  __typename?: 'RequirementSnapshotTag';
  createdAt: Scalars['DateTime']['output'];
  id?: Maybe<Scalars['String']['output']>;
  text?: Maybe<Scalars['String']['output']>;
};

export type TaskStatus = TaskStatusDone | TaskStatusError | TaskStatusPending | TaskStatusProcessing;

export type TaskStatusDone = {
  __typename?: 'TaskStatusDone';
  status?: Maybe<Scalars['String']['output']>;
};

export type TaskStatusError = {
  __typename?: 'TaskStatusError';
  errorMessage?: Maybe<Scalars['String']['output']>;
};

export type TaskStatusPending = {
  __typename?: 'TaskStatusPending';
  status?: Maybe<Scalars['String']['output']>;
};

export type TaskStatusProcessing = {
  __typename?: 'TaskStatusProcessing';
  completed: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type Verification = {
  __typename?: 'Verification';
  createdAt: Scalars['DateTime']['output'];
  createdBy?: Maybe<Scalars['String']['output']>;
  documentId?: Maybe<Scalars['String']['output']>;
  documentVersion?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  rankingStatus?: Maybe<TaskStatus>;
  reportingStatus?: Maybe<TaskStatus>;
  snapshotId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<VerificationStatus>;
  verifyingStatus?: Maybe<TaskStatus>;
};

export type VerificationStatus = VerificationStatusDeleting | VerificationStatusDone | VerificationStatusError | VerificationStatusRunning;

export type VerificationStatusDeleting = {
  __typename?: 'VerificationStatusDeleting';
  status?: Maybe<Scalars['String']['output']>;
};

export type VerificationStatusDone = {
  __typename?: 'VerificationStatusDone';
  status?: Maybe<Scalars['String']['output']>;
};

export type VerificationStatusError = {
  __typename?: 'VerificationStatusError';
  status?: Maybe<Scalars['String']['output']>;
};

export enum VerificationStatusFilter {
  All = 'ALL',
  Done = 'DONE',
  Error = 'ERROR',
  Running = 'RUNNING'
}

export type VerificationStatusRunning = {
  __typename?: 'VerificationStatusRunning';
  status?: Maybe<Scalars['String']['output']>;
};

export type VerificationTag = {
  __typename?: 'VerificationTag';
  createdAt: Scalars['DateTime']['output'];
  id?: Maybe<Scalars['String']['output']>;
  text?: Maybe<Scalars['String']['output']>;
};

export type CreateApiKeyMutationVariables = Exact<{
  name: Scalars['String']['input'];
  secret: Scalars['String']['input'];
}>;


export type CreateApiKeyMutation = { __typename?: 'Mutation', createApiKey?: { __typename?: 'APIKey', id?: string | null, name?: string | null, owner?: string | null, status: ApiKeyStatus } | null };

export type CreateRequirementTagMutationVariables = Exact<{
  text?: InputMaybe<Scalars['String']['input']>;
}>;


export type CreateRequirementTagMutation = { __typename?: 'Mutation', createRequirementTag?: { __typename?: 'RequirementSnapshotTag', createdAt: any, id?: string | null, text?: string | null } | null };

export type CreateVerificationMutationVariables = Exact<{
  documentId?: InputMaybe<Scalars['String']['input']>;
  documentVersion?: InputMaybe<Scalars['String']['input']>;
  snapshotId?: InputMaybe<Scalars['String']['input']>;
  apiKeyId?: InputMaybe<Scalars['String']['input']>;
}>;


export type CreateVerificationMutation = { __typename?: 'Mutation', createVerification?: { __typename?: 'Verification', createdAt: any, createdBy?: string | null, documentId?: string | null, documentVersion?: string | null, id?: string | null, snapshotId?: string | null, rankingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null, reportingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null, status?: { __typename: 'VerificationStatusDeleting', status?: string | null } | { __typename: 'VerificationStatusDone', status?: string | null } | { __typename: 'VerificationStatusError', status?: string | null } | { __typename: 'VerificationStatusRunning', status?: string | null } | null, verifyingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null } | null };

export type CreateVerificationTagMutationVariables = Exact<{
  text?: InputMaybe<Scalars['String']['input']>;
}>;


export type CreateVerificationTagMutation = { __typename?: 'Mutation', createVerificationTag?: { __typename?: 'VerificationTag', createdAt: any, id?: string | null, text?: string | null } | null };

export type DeleteApiKeyMutationVariables = Exact<{
  sha: Scalars['String']['input'];
}>;


export type DeleteApiKeyMutation = { __typename?: 'Mutation', deleteApiKey: boolean };

export type DeleteRequirementSnapshotMutationVariables = Exact<{
  snapshotId?: InputMaybe<Scalars['String']['input']>;
}>;


export type DeleteRequirementSnapshotMutation = { __typename?: 'Mutation', deleteRequirementSnapshot: boolean };

export type DeleteRequirementTagMutationVariables = Exact<{
  id?: InputMaybe<Scalars['String']['input']>;
  text?: InputMaybe<Scalars['String']['input']>;
}>;


export type DeleteRequirementTagMutation = { __typename?: 'Mutation', deleteRequirementTag: boolean };

export type DeleteVerificationMutationVariables = Exact<{
  verificationId?: InputMaybe<Scalars['String']['input']>;
}>;


export type DeleteVerificationMutation = { __typename?: 'Mutation', deleteVerification: boolean };

export type DeleteVerificationTagMutationVariables = Exact<{
  id?: InputMaybe<Scalars['String']['input']>;
  text?: InputMaybe<Scalars['String']['input']>;
}>;


export type DeleteVerificationTagMutation = { __typename?: 'Mutation', deleteVerificationTag: boolean };

export type ImportRequirementMutationVariables = Exact<{
  jamaIds?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>> | InputMaybe<Scalars['String']['input']>>;
  apiKeyId?: InputMaybe<Scalars['String']['input']>;
}>;


export type ImportRequirementMutation = { __typename?: 'Mutation', importRequirement?: { __typename?: 'RequirementSnapshot', author?: string | null, createdAt: any, id?: string | null, downloadUrl?: string | null, status?: { __typename: 'RequirementSnapshotStatusDeleting', status?: string | null } | { __typename: 'RequirementSnapshotStatusDone', status?: string | null } | { __typename: 'RequirementSnapshotStatusError', errorMessage?: string | null } | { __typename: 'RequirementSnapshotStatusFetching', completed: number, total: number } | { __typename: 'RequirementSnapshotStatusProcessing', completed: number, total: number } | { __typename: 'RequirementSnapshotStatusStarting', status?: string | null } | null } | null };

export type RenameApiKeyMutationVariables = Exact<{
  sha: Scalars['String']['input'];
  before: Scalars['String']['input'];
  after: Scalars['String']['input'];
}>;


export type RenameApiKeyMutation = { __typename?: 'Mutation', renameApiKey?: { __typename?: 'ApiKeyRecord', id?: string | null, name?: string | null, owner?: string | null, status?: string | null } | null };

export type RenameRequirementTagMutationVariables = Exact<{
  tagId?: InputMaybe<Scalars['String']['input']>;
  oldText?: InputMaybe<Scalars['String']['input']>;
  newText?: InputMaybe<Scalars['String']['input']>;
}>;


export type RenameRequirementTagMutation = { __typename?: 'Mutation', renameRequirementTag: boolean };

export type RenameVerificationTagMutationVariables = Exact<{
  tagId?: InputMaybe<Scalars['String']['input']>;
  oldText?: InputMaybe<Scalars['String']['input']>;
  newText?: InputMaybe<Scalars['String']['input']>;
}>;


export type RenameVerificationTagMutation = { __typename?: 'Mutation', renameVerificationTag: boolean };

export type RevokeApiKeyMutationVariables = Exact<{
  sha: Scalars['String']['input'];
  email: Scalars['String']['input'];
}>;


export type RevokeApiKeyMutation = { __typename?: 'Mutation', revokeApiKey: boolean };

export type ShareApiKeyMutationVariables = Exact<{
  sha: Scalars['String']['input'];
  email: Scalars['String']['input'];
}>;


export type ShareApiKeyMutation = { __typename?: 'Mutation', shareApiKey?: { __typename?: 'ApiKeyAccessRight', keyID?: string | null, accessorEmail?: string | null } | null };

export type TransferApiKeyMutationVariables = Exact<{
  sha: Scalars['String']['input'];
  email: Scalars['String']['input'];
}>;


export type TransferApiKeyMutation = { __typename?: 'Mutation', transferApiKey: boolean };

export type TagRequirementSnapshotMutationVariables = Exact<{
  snapshotId?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
  tagText?: InputMaybe<Scalars['String']['input']>;
}>;


export type TagRequirementSnapshotMutation = { __typename?: 'Mutation', tagRequirementSnapshot: boolean };

export type UntagRequirementSnapshotMutationVariables = Exact<{
  snapshotId?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
}>;


export type UntagRequirementSnapshotMutation = { __typename?: 'Mutation', untagRequirementSnapshot: boolean };

export type TagVerificationMutationVariables = Exact<{
  verificationId?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
  tagText?: InputMaybe<Scalars['String']['input']>;
}>;


export type TagVerificationMutation = { __typename?: 'Mutation', tagVerification: boolean };

export type UntagVerificationMutationVariables = Exact<{
  verificationId?: InputMaybe<Scalars['String']['input']>;
  tagId?: InputMaybe<Scalars['String']['input']>;
}>;


export type UntagVerificationMutation = { __typename?: 'Mutation', untagVerification: boolean };

export type CreateDocumentMutationVariables = Exact<{
  documentId: Scalars['String']['input'];
  version: Scalars['String']['input'];
  apiKeyId?: InputMaybe<Scalars['String']['input']>;
}>;


export type CreateDocumentMutation = { __typename?: 'Mutation', createDocument?: { __typename?: 'Document', documentId?: string | null, startTime: any, updatedBy?: string | null, version?: string | null, endTime?: { __typename?: 'FSharpOptionOfDateTime', value: any } | null, status?: { __typename: 'DocumentStatusAnnotating', current: number, total: number } | { __typename: 'DocumentStatusComplete', status?: string | null } | { __typename: 'DocumentStatusEmbedding', current: number, total: number } | { __typename: 'DocumentStatusError', errorMessage?: string | null } | { __typename: 'DocumentStatusSplitting', status?: string | null } | { __typename: 'DocumentStatusStarted', status?: string | null } | { __typename: 'DocumentStatusUploaded', status?: string | null } | null, uploadUrl?: { __typename?: 'FSharpOptionOfString', value?: string | null } | null } | null };

export type DeleteDocumentMutationVariables = Exact<{
  documentId: Scalars['String']['input'];
  version: Scalars['String']['input'];
}>;


export type DeleteDocumentMutation = { __typename?: 'Mutation', deleteDocument: boolean };

export type GetAccessibleKeysQueryVariables = Exact<{
  token?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Int']['input'];
}>;


export type GetAccessibleKeysQuery = { __typename?: 'Query', accessibleKeys?: { __typename?: 'PaginatedOfAPIKey', nextToken?: string | null, items?: Array<{ __typename?: 'APIKey', id?: string | null, name?: string | null, owner?: string | null, status: ApiKeyStatus } | null> | null } | null };

export type GetAccessorsQueryVariables = Exact<{
  token?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Int']['input'];
  sha: Scalars['String']['input'];
}>;


export type GetAccessorsQuery = { __typename?: 'Query', accessors?: { __typename?: 'PaginatedOfApiKeyAccessRight', nextToken?: string | null, items?: Array<{ __typename?: 'ApiKeyAccessRight', keyID?: string | null, accessorEmail?: string | null } | null> | null } | null };

export type GetApiKeyQueryVariables = Exact<{
  sha: Scalars['String']['input'];
}>;


export type GetApiKeyQuery = { __typename?: 'Query', apiKey?: { __typename?: 'APIKey', id?: string | null, name?: string | null, owner?: string | null, status: ApiKeyStatus } | null };

export type GetDocumentQueryVariables = Exact<{
  documentId: Scalars['String']['input'];
  version: Scalars['String']['input'];
}>;


export type GetDocumentQuery = { __typename?: 'Query', document?: { __typename?: 'Document', documentId?: string | null, startTime: any, updatedBy?: string | null, version?: string | null, endTime?: { __typename?: 'FSharpOptionOfDateTime', value: any } | null, status?: { __typename: 'DocumentStatusAnnotating', current: number, total: number } | { __typename: 'DocumentStatusComplete', status?: string | null } | { __typename: 'DocumentStatusEmbedding', current: number, total: number } | { __typename: 'DocumentStatusError', errorMessage?: string | null } | { __typename: 'DocumentStatusSplitting', status?: string | null } | { __typename: 'DocumentStatusStarted', status?: string | null } | { __typename: 'DocumentStatusUploaded', status?: string | null } | null, uploadUrl?: { __typename?: 'FSharpOptionOfString', value?: string | null } | null } | null };

export type GetDocumentsQueryVariables = Exact<{
  token?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Int']['input'];
  mineOnly: Scalars['Boolean']['input'];
  status: DocumentStatusFilter;
  first: Scalars['DateTime']['input'];
  last: Scalars['DateTime']['input'];
}>;


export type GetDocumentsQuery = { __typename?: 'Query', documents?: { __typename?: 'PaginatedOfDocument', nextToken?: string | null, items?: Array<{ __typename?: 'Document', documentId?: string | null, startTime: any, updatedBy?: string | null, version?: string | null, endTime?: { __typename?: 'FSharpOptionOfDateTime', value: any } | null, status?: { __typename: 'DocumentStatusAnnotating', current: number, total: number } | { __typename: 'DocumentStatusComplete', status?: string | null } | { __typename: 'DocumentStatusEmbedding', current: number, total: number } | { __typename: 'DocumentStatusError', errorMessage?: string | null } | { __typename: 'DocumentStatusSplitting', status?: string | null } | { __typename: 'DocumentStatusStarted', status?: string | null } | { __typename: 'DocumentStatusUploaded', status?: string | null } | null, uploadUrl?: { __typename?: 'FSharpOptionOfString', value?: string | null } | null } | null> | null } | null };

export type GetOwnedKeysQueryVariables = Exact<{
  token?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Int']['input'];
}>;


export type GetOwnedKeysQuery = { __typename?: 'Query', ownedKeys?: { __typename?: 'PaginatedOfAPIKey', nextToken?: string | null, items?: Array<{ __typename?: 'APIKey', id?: string | null, name?: string | null, owner?: string | null, status: ApiKeyStatus } | null> | null } | null };

export type GetSecretQueryVariables = Exact<{
  sha: Scalars['String']['input'];
}>;


export type GetSecretQuery = { __typename?: 'Query', secret?: string | null };

export type GetRequirementSnapshotQueryVariables = Exact<{
  snapshotId?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetRequirementSnapshotQuery = { __typename?: 'Query', requirementSnapshot?: { __typename?: 'RequirementSnapshot', author?: string | null, createdAt: any, id?: string | null, downloadUrl?: string | null, status?: { __typename: 'RequirementSnapshotStatusDeleting', status?: string | null } | { __typename: 'RequirementSnapshotStatusDone', status?: string | null } | { __typename: 'RequirementSnapshotStatusError', errorMessage?: string | null } | { __typename: 'RequirementSnapshotStatusFetching', completed: number, total: number } | { __typename: 'RequirementSnapshotStatusProcessing', completed: number, total: number } | { __typename: 'RequirementSnapshotStatusStarting', status?: string | null } | null, tags?: Array<{ __typename?: 'RequirementSnapshotTag', id?: string | null, text?: string | null, createdAt: any } | null> | null } | null };

export type GetRequirementSnapshotsQueryVariables = Exact<{
  token?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Int']['input'];
  mineOnly: Scalars['Boolean']['input'];
  statusFilter: RequirementSnapshotStatusFilter;
  first: Scalars['DateTime']['input'];
  last: Scalars['DateTime']['input'];
}>;


export type GetRequirementSnapshotsQuery = { __typename?: 'Query', requirementSnapshots?: { __typename?: 'PaginatedOfRequirementSnapshot', nextToken?: string | null, items?: Array<{ __typename?: 'RequirementSnapshot', author?: string | null, createdAt: any, id?: string | null, downloadUrl?: string | null, status?: { __typename: 'RequirementSnapshotStatusDeleting', status?: string | null } | { __typename: 'RequirementSnapshotStatusDone', status?: string | null } | { __typename: 'RequirementSnapshotStatusError', errorMessage?: string | null } | { __typename: 'RequirementSnapshotStatusFetching', completed: number, total: number } | { __typename: 'RequirementSnapshotStatusProcessing', completed: number, total: number } | { __typename: 'RequirementSnapshotStatusStarting', status?: string | null } | null, tags?: Array<{ __typename?: 'RequirementSnapshotTag', id?: string | null, text?: string | null, createdAt: any } | null> | null } | null> | null } | null };

export type GetSnapshotsWithTagQueryVariables = Exact<{
  token?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Int']['input'];
  tag?: InputMaybe<Scalars['String']['input']>;
  first: Scalars['DateTime']['input'];
  last: Scalars['DateTime']['input'];
}>;


export type GetSnapshotsWithTagQuery = { __typename?: 'Query', snapshotsWithTag?: { __typename?: 'PaginatedOfRequirementSnapshot', nextToken?: string | null, items?: Array<{ __typename?: 'RequirementSnapshot', author?: string | null, createdAt: any, id?: string | null, downloadUrl?: string | null, status?: { __typename: 'RequirementSnapshotStatusDeleting', status?: string | null } | { __typename: 'RequirementSnapshotStatusDone', status?: string | null } | { __typename: 'RequirementSnapshotStatusError', errorMessage?: string | null } | { __typename: 'RequirementSnapshotStatusFetching', completed: number, total: number } | { __typename: 'RequirementSnapshotStatusProcessing', completed: number, total: number } | { __typename: 'RequirementSnapshotStatusStarting', status?: string | null } | null, tags?: Array<{ __typename?: 'RequirementSnapshotTag', id?: string | null, text?: string | null, createdAt: any } | null> | null } | null> | null } | null };

export type GetRequirementTagQueryVariables = Exact<{
  tagId?: InputMaybe<Scalars['String']['input']>;
  text?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetRequirementTagQuery = { __typename?: 'Query', requirementTag?: { __typename?: 'RequirementSnapshotTag', createdAt: any, id?: string | null, text?: string | null } | null };

export type GetRequirementTagsQueryVariables = Exact<{
  token?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Int']['input'];
  prefix?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetRequirementTagsQuery = { __typename?: 'Query', requirementTags?: { __typename?: 'PaginatedOfRequirementSnapshotTag', nextToken?: string | null, items?: Array<{ __typename?: 'RequirementSnapshotTag', createdAt: any, id?: string | null, text?: string | null } | null> | null } | null };

export type GetVerificationQueryVariables = Exact<{
  verificationId?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetVerificationQuery = { __typename?: 'Query', verification?: { __typename?: 'Verification', createdAt: any, createdBy?: string | null, documentId?: string | null, documentVersion?: string | null, id?: string | null, snapshotId?: string | null, rankingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null, reportingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null, status?: { __typename: 'VerificationStatusDeleting', status?: string | null } | { __typename: 'VerificationStatusDone', status?: string | null } | { __typename: 'VerificationStatusError', status?: string | null } | { __typename: 'VerificationStatusRunning', status?: string | null } | null, verifyingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null } | null };

export type GetVerificationsQueryVariables = Exact<{
  token?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Int']['input'];
  first: Scalars['DateTime']['input'];
  last: Scalars['DateTime']['input'];
  mineOnly: Scalars['Boolean']['input'];
  statusFilter: VerificationStatusFilter;
}>;


export type GetVerificationsQuery = { __typename?: 'Query', verifications?: { __typename?: 'PaginatedOfVerification', nextToken?: string | null, items?: Array<{ __typename?: 'Verification', createdAt: any, createdBy?: string | null, documentId?: string | null, documentVersion?: string | null, id?: string | null, snapshotId?: string | null, rankingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null, reportingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null, status?: { __typename: 'VerificationStatusDeleting', status?: string | null } | { __typename: 'VerificationStatusDone', status?: string | null } | { __typename: 'VerificationStatusError', status?: string | null } | { __typename: 'VerificationStatusRunning', status?: string | null } | null, verifyingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null } | null> | null } | null };

export type GetVerificationsWithTagQueryVariables = Exact<{
  token?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Int']['input'];
  tag?: InputMaybe<Scalars['String']['input']>;
  first: Scalars['DateTime']['input'];
  last: Scalars['DateTime']['input'];
}>;


export type GetVerificationsWithTagQuery = { __typename?: 'Query', verificationsWithTag?: { __typename?: 'PaginatedOfVerification', nextToken?: string | null, items?: Array<{ __typename?: 'Verification', createdAt: any, createdBy?: string | null, documentId?: string | null, documentVersion?: string | null, id?: string | null, snapshotId?: string | null, rankingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null, reportingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null, status?: { __typename: 'VerificationStatusDeleting', status?: string | null } | { __typename: 'VerificationStatusDone', status?: string | null } | { __typename: 'VerificationStatusError', status?: string | null } | { __typename: 'VerificationStatusRunning', status?: string | null } | null, verifyingStatus?: { __typename: 'TaskStatusDone', status?: string | null } | { __typename: 'TaskStatusError', errorMessage?: string | null } | { __typename: 'TaskStatusPending', status?: string | null } | { __typename: 'TaskStatusProcessing', completed: number, total: number } | null } | null> | null } | null };

export type GetVerificationTagQueryVariables = Exact<{
  tagId?: InputMaybe<Scalars['String']['input']>;
  text?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetVerificationTagQuery = { __typename?: 'Query', verificationTag?: { __typename?: 'VerificationTag', createdAt: any, id?: string | null, text?: string | null } | null };

export type GetVerificationTagsQueryVariables = Exact<{
  token?: InputMaybe<Scalars['String']['input']>;
  limit: Scalars['Int']['input'];
  prefix?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetVerificationTagsQuery = { __typename?: 'Query', verificationTags?: { __typename?: 'PaginatedOfVerificationTag', nextToken?: string | null, items?: Array<{ __typename?: 'VerificationTag', createdAt: any, id?: string | null, text?: string | null } | null> | null } | null };

export type GetVerificationReportUrlQueryVariables = Exact<{
  verificationId?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetVerificationReportUrlQuery = { __typename?: 'Query', verificationReportUrl?: string | null };


export const CreateApiKeyDocument = gql`
    mutation CreateApiKey($name: String!, $secret: String!) {
  createApiKey(name: $name, secret: $secret) {
    id
    name
    owner
    status
  }
}
    `;
export type CreateApiKeyMutationFn = Apollo.MutationFunction<CreateApiKeyMutation, CreateApiKeyMutationVariables>;

/**
 * __useCreateApiKeyMutation__
 *
 * To run a mutation, you first call `useCreateApiKeyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateApiKeyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createApiKeyMutation, { data, loading, error }] = useCreateApiKeyMutation({
 *   variables: {
 *      name: // value for 'name'
 *      secret: // value for 'secret'
 *   },
 * });
 */
export function useCreateApiKeyMutation(baseOptions?: Apollo.MutationHookOptions<CreateApiKeyMutation, CreateApiKeyMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateApiKeyMutation, CreateApiKeyMutationVariables>(CreateApiKeyDocument, options);
      }
export type CreateApiKeyMutationHookResult = ReturnType<typeof useCreateApiKeyMutation>;
export type CreateApiKeyMutationResult = Apollo.MutationResult<CreateApiKeyMutation>;
export type CreateApiKeyMutationOptions = Apollo.BaseMutationOptions<CreateApiKeyMutation, CreateApiKeyMutationVariables>;
export const CreateRequirementTagDocument = gql`
    mutation CreateRequirementTag($text: String) {
  createRequirementTag(text: $text) {
    createdAt
    id
    text
  }
}
    `;
export type CreateRequirementTagMutationFn = Apollo.MutationFunction<CreateRequirementTagMutation, CreateRequirementTagMutationVariables>;

/**
 * __useCreateRequirementTagMutation__
 *
 * To run a mutation, you first call `useCreateRequirementTagMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateRequirementTagMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createRequirementTagMutation, { data, loading, error }] = useCreateRequirementTagMutation({
 *   variables: {
 *      text: // value for 'text'
 *   },
 * });
 */
export function useCreateRequirementTagMutation(baseOptions?: Apollo.MutationHookOptions<CreateRequirementTagMutation, CreateRequirementTagMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateRequirementTagMutation, CreateRequirementTagMutationVariables>(CreateRequirementTagDocument, options);
      }
export type CreateRequirementTagMutationHookResult = ReturnType<typeof useCreateRequirementTagMutation>;
export type CreateRequirementTagMutationResult = Apollo.MutationResult<CreateRequirementTagMutation>;
export type CreateRequirementTagMutationOptions = Apollo.BaseMutationOptions<CreateRequirementTagMutation, CreateRequirementTagMutationVariables>;
export const CreateVerificationDocument = gql`
    mutation CreateVerification($documentId: String, $documentVersion: String, $snapshotId: String, $apiKeyId: String) {
  createVerification(
    documentId: $documentId
    documentVersion: $documentVersion
    snapshotId: $snapshotId
    apiKeyId: $apiKeyId
  ) {
    createdAt
    createdBy
    documentId
    documentVersion
    id
    rankingStatus {
      __typename
      ... on TaskStatusDone {
        status
      }
      ... on TaskStatusError {
        errorMessage
      }
      ... on TaskStatusPending {
        status
      }
      ... on TaskStatusProcessing {
        completed
        total
      }
    }
    reportingStatus {
      __typename
      ... on TaskStatusDone {
        status
      }
      ... on TaskStatusError {
        errorMessage
      }
      ... on TaskStatusPending {
        status
      }
      ... on TaskStatusProcessing {
        completed
        total
      }
    }
    snapshotId
    status {
      __typename
      ... on VerificationStatusDeleting {
        status
      }
      ... on VerificationStatusDone {
        status
      }
      ... on VerificationStatusError {
        status
      }
      ... on VerificationStatusRunning {
        status
      }
    }
    verifyingStatus {
      __typename
      ... on TaskStatusDone {
        status
      }
      ... on TaskStatusError {
        errorMessage
      }
      ... on TaskStatusPending {
        status
      }
      ... on TaskStatusProcessing {
        completed
        total
      }
    }
  }
}
    `;
export type CreateVerificationMutationFn = Apollo.MutationFunction<CreateVerificationMutation, CreateVerificationMutationVariables>;

/**
 * __useCreateVerificationMutation__
 *
 * To run a mutation, you first call `useCreateVerificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateVerificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createVerificationMutation, { data, loading, error }] = useCreateVerificationMutation({
 *   variables: {
 *      documentId: // value for 'documentId'
 *      documentVersion: // value for 'documentVersion'
 *      snapshotId: // value for 'snapshotId'
 *      apiKeyId: // value for 'apiKeyId'
 *   },
 * });
 */
export function useCreateVerificationMutation(baseOptions?: Apollo.MutationHookOptions<CreateVerificationMutation, CreateVerificationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateVerificationMutation, CreateVerificationMutationVariables>(CreateVerificationDocument, options);
      }
export type CreateVerificationMutationHookResult = ReturnType<typeof useCreateVerificationMutation>;
export type CreateVerificationMutationResult = Apollo.MutationResult<CreateVerificationMutation>;
export type CreateVerificationMutationOptions = Apollo.BaseMutationOptions<CreateVerificationMutation, CreateVerificationMutationVariables>;
export const CreateVerificationTagDocument = gql`
    mutation CreateVerificationTag($text: String) {
  createVerificationTag(text: $text) {
    createdAt
    id
    text
  }
}
    `;
export type CreateVerificationTagMutationFn = Apollo.MutationFunction<CreateVerificationTagMutation, CreateVerificationTagMutationVariables>;

/**
 * __useCreateVerificationTagMutation__
 *
 * To run a mutation, you first call `useCreateVerificationTagMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateVerificationTagMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createVerificationTagMutation, { data, loading, error }] = useCreateVerificationTagMutation({
 *   variables: {
 *      text: // value for 'text'
 *   },
 * });
 */
export function useCreateVerificationTagMutation(baseOptions?: Apollo.MutationHookOptions<CreateVerificationTagMutation, CreateVerificationTagMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateVerificationTagMutation, CreateVerificationTagMutationVariables>(CreateVerificationTagDocument, options);
      }
export type CreateVerificationTagMutationHookResult = ReturnType<typeof useCreateVerificationTagMutation>;
export type CreateVerificationTagMutationResult = Apollo.MutationResult<CreateVerificationTagMutation>;
export type CreateVerificationTagMutationOptions = Apollo.BaseMutationOptions<CreateVerificationTagMutation, CreateVerificationTagMutationVariables>;
export const DeleteApiKeyDocument = gql`
    mutation DeleteApiKey($sha: String!) {
  deleteApiKey(sha: $sha)
}
    `;
export type DeleteApiKeyMutationFn = Apollo.MutationFunction<DeleteApiKeyMutation, DeleteApiKeyMutationVariables>;

/**
 * __useDeleteApiKeyMutation__
 *
 * To run a mutation, you first call `useDeleteApiKeyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteApiKeyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteApiKeyMutation, { data, loading, error }] = useDeleteApiKeyMutation({
 *   variables: {
 *      sha: // value for 'sha'
 *   },
 * });
 */
export function useDeleteApiKeyMutation(baseOptions?: Apollo.MutationHookOptions<DeleteApiKeyMutation, DeleteApiKeyMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteApiKeyMutation, DeleteApiKeyMutationVariables>(DeleteApiKeyDocument, options);
      }
export type DeleteApiKeyMutationHookResult = ReturnType<typeof useDeleteApiKeyMutation>;
export type DeleteApiKeyMutationResult = Apollo.MutationResult<DeleteApiKeyMutation>;
export type DeleteApiKeyMutationOptions = Apollo.BaseMutationOptions<DeleteApiKeyMutation, DeleteApiKeyMutationVariables>;
export const DeleteRequirementSnapshotDocument = gql`
    mutation DeleteRequirementSnapshot($snapshotId: String) {
  deleteRequirementSnapshot(snapshotId: $snapshotId)
}
    `;
export type DeleteRequirementSnapshotMutationFn = Apollo.MutationFunction<DeleteRequirementSnapshotMutation, DeleteRequirementSnapshotMutationVariables>;

/**
 * __useDeleteRequirementSnapshotMutation__
 *
 * To run a mutation, you first call `useDeleteRequirementSnapshotMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteRequirementSnapshotMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteRequirementSnapshotMutation, { data, loading, error }] = useDeleteRequirementSnapshotMutation({
 *   variables: {
 *      snapshotId: // value for 'snapshotId'
 *   },
 * });
 */
export function useDeleteRequirementSnapshotMutation(baseOptions?: Apollo.MutationHookOptions<DeleteRequirementSnapshotMutation, DeleteRequirementSnapshotMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteRequirementSnapshotMutation, DeleteRequirementSnapshotMutationVariables>(DeleteRequirementSnapshotDocument, options);
      }
export type DeleteRequirementSnapshotMutationHookResult = ReturnType<typeof useDeleteRequirementSnapshotMutation>;
export type DeleteRequirementSnapshotMutationResult = Apollo.MutationResult<DeleteRequirementSnapshotMutation>;
export type DeleteRequirementSnapshotMutationOptions = Apollo.BaseMutationOptions<DeleteRequirementSnapshotMutation, DeleteRequirementSnapshotMutationVariables>;
export const DeleteRequirementTagDocument = gql`
    mutation DeleteRequirementTag($id: String, $text: String) {
  deleteRequirementTag(id: $id, text: $text)
}
    `;
export type DeleteRequirementTagMutationFn = Apollo.MutationFunction<DeleteRequirementTagMutation, DeleteRequirementTagMutationVariables>;

/**
 * __useDeleteRequirementTagMutation__
 *
 * To run a mutation, you first call `useDeleteRequirementTagMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteRequirementTagMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteRequirementTagMutation, { data, loading, error }] = useDeleteRequirementTagMutation({
 *   variables: {
 *      id: // value for 'id'
 *      text: // value for 'text'
 *   },
 * });
 */
export function useDeleteRequirementTagMutation(baseOptions?: Apollo.MutationHookOptions<DeleteRequirementTagMutation, DeleteRequirementTagMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteRequirementTagMutation, DeleteRequirementTagMutationVariables>(DeleteRequirementTagDocument, options);
      }
export type DeleteRequirementTagMutationHookResult = ReturnType<typeof useDeleteRequirementTagMutation>;
export type DeleteRequirementTagMutationResult = Apollo.MutationResult<DeleteRequirementTagMutation>;
export type DeleteRequirementTagMutationOptions = Apollo.BaseMutationOptions<DeleteRequirementTagMutation, DeleteRequirementTagMutationVariables>;
export const DeleteVerificationDocument = gql`
    mutation DeleteVerification($verificationId: String) {
  deleteVerification(verificationId: $verificationId)
}
    `;
export type DeleteVerificationMutationFn = Apollo.MutationFunction<DeleteVerificationMutation, DeleteVerificationMutationVariables>;

/**
 * __useDeleteVerificationMutation__
 *
 * To run a mutation, you first call `useDeleteVerificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteVerificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteVerificationMutation, { data, loading, error }] = useDeleteVerificationMutation({
 *   variables: {
 *      verificationId: // value for 'verificationId'
 *   },
 * });
 */
export function useDeleteVerificationMutation(baseOptions?: Apollo.MutationHookOptions<DeleteVerificationMutation, DeleteVerificationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteVerificationMutation, DeleteVerificationMutationVariables>(DeleteVerificationDocument, options);
      }
export type DeleteVerificationMutationHookResult = ReturnType<typeof useDeleteVerificationMutation>;
export type DeleteVerificationMutationResult = Apollo.MutationResult<DeleteVerificationMutation>;
export type DeleteVerificationMutationOptions = Apollo.BaseMutationOptions<DeleteVerificationMutation, DeleteVerificationMutationVariables>;
export const DeleteVerificationTagDocument = gql`
    mutation DeleteVerificationTag($id: String, $text: String) {
  deleteVerificationTag(id: $id, text: $text)
}
    `;
export type DeleteVerificationTagMutationFn = Apollo.MutationFunction<DeleteVerificationTagMutation, DeleteVerificationTagMutationVariables>;

/**
 * __useDeleteVerificationTagMutation__
 *
 * To run a mutation, you first call `useDeleteVerificationTagMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteVerificationTagMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteVerificationTagMutation, { data, loading, error }] = useDeleteVerificationTagMutation({
 *   variables: {
 *      id: // value for 'id'
 *      text: // value for 'text'
 *   },
 * });
 */
export function useDeleteVerificationTagMutation(baseOptions?: Apollo.MutationHookOptions<DeleteVerificationTagMutation, DeleteVerificationTagMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteVerificationTagMutation, DeleteVerificationTagMutationVariables>(DeleteVerificationTagDocument, options);
      }
export type DeleteVerificationTagMutationHookResult = ReturnType<typeof useDeleteVerificationTagMutation>;
export type DeleteVerificationTagMutationResult = Apollo.MutationResult<DeleteVerificationTagMutation>;
export type DeleteVerificationTagMutationOptions = Apollo.BaseMutationOptions<DeleteVerificationTagMutation, DeleteVerificationTagMutationVariables>;
export const ImportRequirementDocument = gql`
    mutation ImportRequirement($jamaIds: [String], $apiKeyId: String) {
  importRequirement(jamaIds: $jamaIds, apiKeyId: $apiKeyId) {
    author
    createdAt
    id
    status {
      __typename
      ... on RequirementSnapshotStatusStarting {
        status
      }
      ... on RequirementSnapshotStatusFetching {
        completed
        total
      }
      ... on RequirementSnapshotStatusProcessing {
        completed
        total
      }
      ... on RequirementSnapshotStatusDone {
        status
      }
      ... on RequirementSnapshotStatusError {
        errorMessage
      }
      ... on RequirementSnapshotStatusDeleting {
        status
      }
    }
    downloadUrl
  }
}
    `;
export type ImportRequirementMutationFn = Apollo.MutationFunction<ImportRequirementMutation, ImportRequirementMutationVariables>;

/**
 * __useImportRequirementMutation__
 *
 * To run a mutation, you first call `useImportRequirementMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useImportRequirementMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [importRequirementMutation, { data, loading, error }] = useImportRequirementMutation({
 *   variables: {
 *      jamaIds: // value for 'jamaIds'
 *      apiKeyId: // value for 'apiKeyId'
 *   },
 * });
 */
export function useImportRequirementMutation(baseOptions?: Apollo.MutationHookOptions<ImportRequirementMutation, ImportRequirementMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ImportRequirementMutation, ImportRequirementMutationVariables>(ImportRequirementDocument, options);
      }
export type ImportRequirementMutationHookResult = ReturnType<typeof useImportRequirementMutation>;
export type ImportRequirementMutationResult = Apollo.MutationResult<ImportRequirementMutation>;
export type ImportRequirementMutationOptions = Apollo.BaseMutationOptions<ImportRequirementMutation, ImportRequirementMutationVariables>;
export const RenameApiKeyDocument = gql`
    mutation RenameApiKey($sha: String!, $before: String!, $after: String!) {
  renameApiKey(sha: $sha, before: $before, after: $after) {
    id
    name
    owner
    status
  }
}
    `;
export type RenameApiKeyMutationFn = Apollo.MutationFunction<RenameApiKeyMutation, RenameApiKeyMutationVariables>;

/**
 * __useRenameApiKeyMutation__
 *
 * To run a mutation, you first call `useRenameApiKeyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRenameApiKeyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [renameApiKeyMutation, { data, loading, error }] = useRenameApiKeyMutation({
 *   variables: {
 *      sha: // value for 'sha'
 *      before: // value for 'before'
 *      after: // value for 'after'
 *   },
 * });
 */
export function useRenameApiKeyMutation(baseOptions?: Apollo.MutationHookOptions<RenameApiKeyMutation, RenameApiKeyMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<RenameApiKeyMutation, RenameApiKeyMutationVariables>(RenameApiKeyDocument, options);
      }
export type RenameApiKeyMutationHookResult = ReturnType<typeof useRenameApiKeyMutation>;
export type RenameApiKeyMutationResult = Apollo.MutationResult<RenameApiKeyMutation>;
export type RenameApiKeyMutationOptions = Apollo.BaseMutationOptions<RenameApiKeyMutation, RenameApiKeyMutationVariables>;
export const RenameRequirementTagDocument = gql`
    mutation RenameRequirementTag($tagId: String, $oldText: String, $newText: String) {
  renameRequirementTag(tagId: $tagId, oldText: $oldText, newText: $newText)
}
    `;
export type RenameRequirementTagMutationFn = Apollo.MutationFunction<RenameRequirementTagMutation, RenameRequirementTagMutationVariables>;

/**
 * __useRenameRequirementTagMutation__
 *
 * To run a mutation, you first call `useRenameRequirementTagMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRenameRequirementTagMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [renameRequirementTagMutation, { data, loading, error }] = useRenameRequirementTagMutation({
 *   variables: {
 *      tagId: // value for 'tagId'
 *      oldText: // value for 'oldText'
 *      newText: // value for 'newText'
 *   },
 * });
 */
export function useRenameRequirementTagMutation(baseOptions?: Apollo.MutationHookOptions<RenameRequirementTagMutation, RenameRequirementTagMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<RenameRequirementTagMutation, RenameRequirementTagMutationVariables>(RenameRequirementTagDocument, options);
      }
export type RenameRequirementTagMutationHookResult = ReturnType<typeof useRenameRequirementTagMutation>;
export type RenameRequirementTagMutationResult = Apollo.MutationResult<RenameRequirementTagMutation>;
export type RenameRequirementTagMutationOptions = Apollo.BaseMutationOptions<RenameRequirementTagMutation, RenameRequirementTagMutationVariables>;
export const RenameVerificationTagDocument = gql`
    mutation RenameVerificationTag($tagId: String, $oldText: String, $newText: String) {
  renameVerificationTag(tagId: $tagId, oldText: $oldText, newText: $newText)
}
    `;
export type RenameVerificationTagMutationFn = Apollo.MutationFunction<RenameVerificationTagMutation, RenameVerificationTagMutationVariables>;

/**
 * __useRenameVerificationTagMutation__
 *
 * To run a mutation, you first call `useRenameVerificationTagMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRenameVerificationTagMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [renameVerificationTagMutation, { data, loading, error }] = useRenameVerificationTagMutation({
 *   variables: {
 *      tagId: // value for 'tagId'
 *      oldText: // value for 'oldText'
 *      newText: // value for 'newText'
 *   },
 * });
 */
export function useRenameVerificationTagMutation(baseOptions?: Apollo.MutationHookOptions<RenameVerificationTagMutation, RenameVerificationTagMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<RenameVerificationTagMutation, RenameVerificationTagMutationVariables>(RenameVerificationTagDocument, options);
      }
export type RenameVerificationTagMutationHookResult = ReturnType<typeof useRenameVerificationTagMutation>;
export type RenameVerificationTagMutationResult = Apollo.MutationResult<RenameVerificationTagMutation>;
export type RenameVerificationTagMutationOptions = Apollo.BaseMutationOptions<RenameVerificationTagMutation, RenameVerificationTagMutationVariables>;
export const RevokeApiKeyDocument = gql`
    mutation RevokeApiKey($sha: String!, $email: String!) {
  revokeApiKey(sha: $sha, email: $email)
}
    `;
export type RevokeApiKeyMutationFn = Apollo.MutationFunction<RevokeApiKeyMutation, RevokeApiKeyMutationVariables>;

/**
 * __useRevokeApiKeyMutation__
 *
 * To run a mutation, you first call `useRevokeApiKeyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRevokeApiKeyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [revokeApiKeyMutation, { data, loading, error }] = useRevokeApiKeyMutation({
 *   variables: {
 *      sha: // value for 'sha'
 *      email: // value for 'email'
 *   },
 * });
 */
export function useRevokeApiKeyMutation(baseOptions?: Apollo.MutationHookOptions<RevokeApiKeyMutation, RevokeApiKeyMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<RevokeApiKeyMutation, RevokeApiKeyMutationVariables>(RevokeApiKeyDocument, options);
      }
export type RevokeApiKeyMutationHookResult = ReturnType<typeof useRevokeApiKeyMutation>;
export type RevokeApiKeyMutationResult = Apollo.MutationResult<RevokeApiKeyMutation>;
export type RevokeApiKeyMutationOptions = Apollo.BaseMutationOptions<RevokeApiKeyMutation, RevokeApiKeyMutationVariables>;
export const ShareApiKeyDocument = gql`
    mutation ShareApiKey($sha: String!, $email: String!) {
  shareApiKey(sha: $sha, email: $email) {
    keyID
    accessorEmail
  }
}
    `;
export type ShareApiKeyMutationFn = Apollo.MutationFunction<ShareApiKeyMutation, ShareApiKeyMutationVariables>;

/**
 * __useShareApiKeyMutation__
 *
 * To run a mutation, you first call `useShareApiKeyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useShareApiKeyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [shareApiKeyMutation, { data, loading, error }] = useShareApiKeyMutation({
 *   variables: {
 *      sha: // value for 'sha'
 *      email: // value for 'email'
 *   },
 * });
 */
export function useShareApiKeyMutation(baseOptions?: Apollo.MutationHookOptions<ShareApiKeyMutation, ShareApiKeyMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ShareApiKeyMutation, ShareApiKeyMutationVariables>(ShareApiKeyDocument, options);
      }
export type ShareApiKeyMutationHookResult = ReturnType<typeof useShareApiKeyMutation>;
export type ShareApiKeyMutationResult = Apollo.MutationResult<ShareApiKeyMutation>;
export type ShareApiKeyMutationOptions = Apollo.BaseMutationOptions<ShareApiKeyMutation, ShareApiKeyMutationVariables>;
export const TransferApiKeyDocument = gql`
    mutation TransferApiKey($sha: String!, $email: String!) {
  transferApiKey(sha: $sha, email: $email)
}
    `;
export type TransferApiKeyMutationFn = Apollo.MutationFunction<TransferApiKeyMutation, TransferApiKeyMutationVariables>;

/**
 * __useTransferApiKeyMutation__
 *
 * To run a mutation, you first call `useTransferApiKeyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useTransferApiKeyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [transferApiKeyMutation, { data, loading, error }] = useTransferApiKeyMutation({
 *   variables: {
 *      sha: // value for 'sha'
 *      email: // value for 'email'
 *   },
 * });
 */
export function useTransferApiKeyMutation(baseOptions?: Apollo.MutationHookOptions<TransferApiKeyMutation, TransferApiKeyMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<TransferApiKeyMutation, TransferApiKeyMutationVariables>(TransferApiKeyDocument, options);
      }
export type TransferApiKeyMutationHookResult = ReturnType<typeof useTransferApiKeyMutation>;
export type TransferApiKeyMutationResult = Apollo.MutationResult<TransferApiKeyMutation>;
export type TransferApiKeyMutationOptions = Apollo.BaseMutationOptions<TransferApiKeyMutation, TransferApiKeyMutationVariables>;
export const TagRequirementSnapshotDocument = gql`
    mutation TagRequirementSnapshot($snapshotId: String, $tagId: String, $tagText: String) {
  tagRequirementSnapshot(
    snapshotId: $snapshotId
    tagId: $tagId
    tagText: $tagText
  )
}
    `;
export type TagRequirementSnapshotMutationFn = Apollo.MutationFunction<TagRequirementSnapshotMutation, TagRequirementSnapshotMutationVariables>;

/**
 * __useTagRequirementSnapshotMutation__
 *
 * To run a mutation, you first call `useTagRequirementSnapshotMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useTagRequirementSnapshotMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [tagRequirementSnapshotMutation, { data, loading, error }] = useTagRequirementSnapshotMutation({
 *   variables: {
 *      snapshotId: // value for 'snapshotId'
 *      tagId: // value for 'tagId'
 *      tagText: // value for 'tagText'
 *   },
 * });
 */
export function useTagRequirementSnapshotMutation(baseOptions?: Apollo.MutationHookOptions<TagRequirementSnapshotMutation, TagRequirementSnapshotMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<TagRequirementSnapshotMutation, TagRequirementSnapshotMutationVariables>(TagRequirementSnapshotDocument, options);
      }
export type TagRequirementSnapshotMutationHookResult = ReturnType<typeof useTagRequirementSnapshotMutation>;
export type TagRequirementSnapshotMutationResult = Apollo.MutationResult<TagRequirementSnapshotMutation>;
export type TagRequirementSnapshotMutationOptions = Apollo.BaseMutationOptions<TagRequirementSnapshotMutation, TagRequirementSnapshotMutationVariables>;
export const UntagRequirementSnapshotDocument = gql`
    mutation UntagRequirementSnapshot($snapshotId: String, $tagId: String) {
  untagRequirementSnapshot(snapshotId: $snapshotId, tagId: $tagId)
}
    `;
export type UntagRequirementSnapshotMutationFn = Apollo.MutationFunction<UntagRequirementSnapshotMutation, UntagRequirementSnapshotMutationVariables>;

/**
 * __useUntagRequirementSnapshotMutation__
 *
 * To run a mutation, you first call `useUntagRequirementSnapshotMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUntagRequirementSnapshotMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [untagRequirementSnapshotMutation, { data, loading, error }] = useUntagRequirementSnapshotMutation({
 *   variables: {
 *      snapshotId: // value for 'snapshotId'
 *      tagId: // value for 'tagId'
 *   },
 * });
 */
export function useUntagRequirementSnapshotMutation(baseOptions?: Apollo.MutationHookOptions<UntagRequirementSnapshotMutation, UntagRequirementSnapshotMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UntagRequirementSnapshotMutation, UntagRequirementSnapshotMutationVariables>(UntagRequirementSnapshotDocument, options);
      }
export type UntagRequirementSnapshotMutationHookResult = ReturnType<typeof useUntagRequirementSnapshotMutation>;
export type UntagRequirementSnapshotMutationResult = Apollo.MutationResult<UntagRequirementSnapshotMutation>;
export type UntagRequirementSnapshotMutationOptions = Apollo.BaseMutationOptions<UntagRequirementSnapshotMutation, UntagRequirementSnapshotMutationVariables>;
export const TagVerificationDocument = gql`
    mutation TagVerification($verificationId: String, $tagId: String, $tagText: String) {
  tagVerification(
    verificationId: $verificationId
    tagId: $tagId
    tagText: $tagText
  )
}
    `;
export type TagVerificationMutationFn = Apollo.MutationFunction<TagVerificationMutation, TagVerificationMutationVariables>;

/**
 * __useTagVerificationMutation__
 *
 * To run a mutation, you first call `useTagVerificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useTagVerificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [tagVerificationMutation, { data, loading, error }] = useTagVerificationMutation({
 *   variables: {
 *      verificationId: // value for 'verificationId'
 *      tagId: // value for 'tagId'
 *      tagText: // value for 'tagText'
 *   },
 * });
 */
export function useTagVerificationMutation(baseOptions?: Apollo.MutationHookOptions<TagVerificationMutation, TagVerificationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<TagVerificationMutation, TagVerificationMutationVariables>(TagVerificationDocument, options);
      }
export type TagVerificationMutationHookResult = ReturnType<typeof useTagVerificationMutation>;
export type TagVerificationMutationResult = Apollo.MutationResult<TagVerificationMutation>;
export type TagVerificationMutationOptions = Apollo.BaseMutationOptions<TagVerificationMutation, TagVerificationMutationVariables>;
export const UntagVerificationDocument = gql`
    mutation UntagVerification($verificationId: String, $tagId: String) {
  untagVerification(verificationId: $verificationId, tagId: $tagId)
}
    `;
export type UntagVerificationMutationFn = Apollo.MutationFunction<UntagVerificationMutation, UntagVerificationMutationVariables>;

/**
 * __useUntagVerificationMutation__
 *
 * To run a mutation, you first call `useUntagVerificationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUntagVerificationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [untagVerificationMutation, { data, loading, error }] = useUntagVerificationMutation({
 *   variables: {
 *      verificationId: // value for 'verificationId'
 *      tagId: // value for 'tagId'
 *   },
 * });
 */
export function useUntagVerificationMutation(baseOptions?: Apollo.MutationHookOptions<UntagVerificationMutation, UntagVerificationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UntagVerificationMutation, UntagVerificationMutationVariables>(UntagVerificationDocument, options);
      }
export type UntagVerificationMutationHookResult = ReturnType<typeof useUntagVerificationMutation>;
export type UntagVerificationMutationResult = Apollo.MutationResult<UntagVerificationMutation>;
export type UntagVerificationMutationOptions = Apollo.BaseMutationOptions<UntagVerificationMutation, UntagVerificationMutationVariables>;
export const CreateDocumentDocument = gql`
    mutation CreateDocument($documentId: String!, $version: String!, $apiKeyId: String) {
  createDocument(documentId: $documentId, version: $version, apiKeyId: $apiKeyId) {
    documentId
    endTime {
      value
    }
    startTime
    status {
      __typename
      ... on DocumentStatusStarted {
        status
      }
      ... on DocumentStatusUploaded {
        status
      }
      ... on DocumentStatusSplitting {
        status
      }
      ... on DocumentStatusAnnotating {
        current
        total
      }
      ... on DocumentStatusEmbedding {
        current
        total
      }
      ... on DocumentStatusComplete {
        status
      }
      ... on DocumentStatusError {
        errorMessage
      }
    }
    updatedBy
    version
    uploadUrl {
      value
    }
  }
}
    `;
export type CreateDocumentMutationFn = Apollo.MutationFunction<CreateDocumentMutation, CreateDocumentMutationVariables>;

/**
 * __useCreateDocumentMutation__
 *
 * To run a mutation, you first call `useCreateDocumentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateDocumentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createDocumentMutation, { data, loading, error }] = useCreateDocumentMutation({
 *   variables: {
 *      documentId: // value for 'documentId'
 *      version: // value for 'version'
 *      apiKeyId: // value for 'apiKeyId'
 *   },
 * });
 */
export function useCreateDocumentMutation(baseOptions?: Apollo.MutationHookOptions<CreateDocumentMutation, CreateDocumentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateDocumentMutation, CreateDocumentMutationVariables>(CreateDocumentDocument, options);
      }
export type CreateDocumentMutationHookResult = ReturnType<typeof useCreateDocumentMutation>;
export type CreateDocumentMutationResult = Apollo.MutationResult<CreateDocumentMutation>;
export type CreateDocumentMutationOptions = Apollo.BaseMutationOptions<CreateDocumentMutation, CreateDocumentMutationVariables>;
export const DeleteDocumentDocument = gql`
    mutation DeleteDocument($documentId: String!, $version: String!) {
  deleteDocument(documentId: $documentId, version: $version)
}
    `;
export type DeleteDocumentMutationFn = Apollo.MutationFunction<DeleteDocumentMutation, DeleteDocumentMutationVariables>;

/**
 * __useDeleteDocumentMutation__
 *
 * To run a mutation, you first call `useDeleteDocumentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteDocumentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteDocumentMutation, { data, loading, error }] = useDeleteDocumentMutation({
 *   variables: {
 *      documentId: // value for 'documentId'
 *      version: // value for 'version'
 *   },
 * });
 */
export function useDeleteDocumentMutation(baseOptions?: Apollo.MutationHookOptions<DeleteDocumentMutation, DeleteDocumentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteDocumentMutation, DeleteDocumentMutationVariables>(DeleteDocumentDocument, options);
      }
export type DeleteDocumentMutationHookResult = ReturnType<typeof useDeleteDocumentMutation>;
export type DeleteDocumentMutationResult = Apollo.MutationResult<DeleteDocumentMutation>;
export type DeleteDocumentMutationOptions = Apollo.BaseMutationOptions<DeleteDocumentMutation, DeleteDocumentMutationVariables>;
export const GetAccessibleKeysDocument = gql`
    query GetAccessibleKeys($token: String, $limit: Int!) {
  accessibleKeys(token: $token, limit: $limit) {
    items {
      id
      name
      owner
      status
    }
    nextToken
  }
}
    `;

/**
 * __useGetAccessibleKeysQuery__
 *
 * To run a query within a React component, call `useGetAccessibleKeysQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAccessibleKeysQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAccessibleKeysQuery({
 *   variables: {
 *      token: // value for 'token'
 *      limit: // value for 'limit'
 *   },
 * });
 */
export function useGetAccessibleKeysQuery(baseOptions: Apollo.QueryHookOptions<GetAccessibleKeysQuery, GetAccessibleKeysQueryVariables> & ({ variables: GetAccessibleKeysQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetAccessibleKeysQuery, GetAccessibleKeysQueryVariables>(GetAccessibleKeysDocument, options);
      }
export function useGetAccessibleKeysLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetAccessibleKeysQuery, GetAccessibleKeysQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetAccessibleKeysQuery, GetAccessibleKeysQueryVariables>(GetAccessibleKeysDocument, options);
        }
export function useGetAccessibleKeysSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetAccessibleKeysQuery, GetAccessibleKeysQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetAccessibleKeysQuery, GetAccessibleKeysQueryVariables>(GetAccessibleKeysDocument, options);
        }
export type GetAccessibleKeysQueryHookResult = ReturnType<typeof useGetAccessibleKeysQuery>;
export type GetAccessibleKeysLazyQueryHookResult = ReturnType<typeof useGetAccessibleKeysLazyQuery>;
export type GetAccessibleKeysSuspenseQueryHookResult = ReturnType<typeof useGetAccessibleKeysSuspenseQuery>;
export type GetAccessibleKeysQueryResult = Apollo.QueryResult<GetAccessibleKeysQuery, GetAccessibleKeysQueryVariables>;
export const GetAccessorsDocument = gql`
    query GetAccessors($token: String, $limit: Int!, $sha: String!) {
  accessors(token: $token, limit: $limit, sha: $sha) {
    items {
      keyID
      accessorEmail
    }
    nextToken
  }
}
    `;

/**
 * __useGetAccessorsQuery__
 *
 * To run a query within a React component, call `useGetAccessorsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAccessorsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAccessorsQuery({
 *   variables: {
 *      token: // value for 'token'
 *      limit: // value for 'limit'
 *      sha: // value for 'sha'
 *   },
 * });
 */
export function useGetAccessorsQuery(baseOptions: Apollo.QueryHookOptions<GetAccessorsQuery, GetAccessorsQueryVariables> & ({ variables: GetAccessorsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetAccessorsQuery, GetAccessorsQueryVariables>(GetAccessorsDocument, options);
      }
export function useGetAccessorsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetAccessorsQuery, GetAccessorsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetAccessorsQuery, GetAccessorsQueryVariables>(GetAccessorsDocument, options);
        }
export function useGetAccessorsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetAccessorsQuery, GetAccessorsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetAccessorsQuery, GetAccessorsQueryVariables>(GetAccessorsDocument, options);
        }
export type GetAccessorsQueryHookResult = ReturnType<typeof useGetAccessorsQuery>;
export type GetAccessorsLazyQueryHookResult = ReturnType<typeof useGetAccessorsLazyQuery>;
export type GetAccessorsSuspenseQueryHookResult = ReturnType<typeof useGetAccessorsSuspenseQuery>;
export type GetAccessorsQueryResult = Apollo.QueryResult<GetAccessorsQuery, GetAccessorsQueryVariables>;
export const GetApiKeyDocument = gql`
    query GetApiKey($sha: String!) {
  apiKey(sha: $sha) {
    id
    name
    owner
    status
  }
}
    `;

/**
 * __useGetApiKeyQuery__
 *
 * To run a query within a React component, call `useGetApiKeyQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiKeyQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiKeyQuery({
 *   variables: {
 *      sha: // value for 'sha'
 *   },
 * });
 */
export function useGetApiKeyQuery(baseOptions: Apollo.QueryHookOptions<GetApiKeyQuery, GetApiKeyQueryVariables> & ({ variables: GetApiKeyQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetApiKeyQuery, GetApiKeyQueryVariables>(GetApiKeyDocument, options);
      }
export function useGetApiKeyLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetApiKeyQuery, GetApiKeyQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetApiKeyQuery, GetApiKeyQueryVariables>(GetApiKeyDocument, options);
        }
export function useGetApiKeySuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetApiKeyQuery, GetApiKeyQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetApiKeyQuery, GetApiKeyQueryVariables>(GetApiKeyDocument, options);
        }
export type GetApiKeyQueryHookResult = ReturnType<typeof useGetApiKeyQuery>;
export type GetApiKeyLazyQueryHookResult = ReturnType<typeof useGetApiKeyLazyQuery>;
export type GetApiKeySuspenseQueryHookResult = ReturnType<typeof useGetApiKeySuspenseQuery>;
export type GetApiKeyQueryResult = Apollo.QueryResult<GetApiKeyQuery, GetApiKeyQueryVariables>;
export const GetDocumentDocument = gql`
    query GetDocument($documentId: String!, $version: String!) {
  document(documentId: $documentId, version: $version) {
    documentId
    endTime {
      value
    }
    startTime
    status {
      __typename
      ... on DocumentStatusStarted {
        status
      }
      ... on DocumentStatusUploaded {
        status
      }
      ... on DocumentStatusSplitting {
        status
      }
      ... on DocumentStatusAnnotating {
        current
        total
      }
      ... on DocumentStatusEmbedding {
        current
        total
      }
      ... on DocumentStatusComplete {
        status
      }
      ... on DocumentStatusError {
        errorMessage
      }
    }
    updatedBy
    version
    uploadUrl {
      value
    }
  }
}
    `;

/**
 * __useGetDocumentQuery__
 *
 * To run a query within a React component, call `useGetDocumentQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetDocumentQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetDocumentQuery({
 *   variables: {
 *      documentId: // value for 'documentId'
 *      version: // value for 'version'
 *   },
 * });
 */
export function useGetDocumentQuery(baseOptions: Apollo.QueryHookOptions<GetDocumentQuery, GetDocumentQueryVariables> & ({ variables: GetDocumentQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetDocumentQuery, GetDocumentQueryVariables>(GetDocumentDocument, options);
      }
export function useGetDocumentLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetDocumentQuery, GetDocumentQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetDocumentQuery, GetDocumentQueryVariables>(GetDocumentDocument, options);
        }
export function useGetDocumentSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetDocumentQuery, GetDocumentQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetDocumentQuery, GetDocumentQueryVariables>(GetDocumentDocument, options);
        }
export type GetDocumentQueryHookResult = ReturnType<typeof useGetDocumentQuery>;
export type GetDocumentLazyQueryHookResult = ReturnType<typeof useGetDocumentLazyQuery>;
export type GetDocumentSuspenseQueryHookResult = ReturnType<typeof useGetDocumentSuspenseQuery>;
export type GetDocumentQueryResult = Apollo.QueryResult<GetDocumentQuery, GetDocumentQueryVariables>;
export const GetDocumentsDocument = gql`
    query GetDocuments($token: String, $limit: Int!, $mineOnly: Boolean!, $status: DocumentStatusFilter!, $first: DateTime!, $last: DateTime!) {
  documents(
    token: $token
    limit: $limit
    mineOnly: $mineOnly
    status: $status
    first: $first
    last: $last
  ) {
    items {
      documentId
      endTime {
        value
      }
      startTime
      status {
        __typename
        ... on DocumentStatusStarted {
          status
        }
        ... on DocumentStatusUploaded {
          status
        }
        ... on DocumentStatusSplitting {
          status
        }
        ... on DocumentStatusAnnotating {
          current
          total
        }
        ... on DocumentStatusEmbedding {
          current
          total
        }
        ... on DocumentStatusComplete {
          status
        }
        ... on DocumentStatusError {
          errorMessage
        }
      }
      updatedBy
      version
      uploadUrl {
        value
      }
    }
    nextToken
  }
}
    `;

/**
 * __useGetDocumentsQuery__
 *
 * To run a query within a React component, call `useGetDocumentsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetDocumentsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetDocumentsQuery({
 *   variables: {
 *      token: // value for 'token'
 *      limit: // value for 'limit'
 *      mineOnly: // value for 'mineOnly'
 *      status: // value for 'status'
 *      first: // value for 'first'
 *      last: // value for 'last'
 *   },
 * });
 */
export function useGetDocumentsQuery(baseOptions: Apollo.QueryHookOptions<GetDocumentsQuery, GetDocumentsQueryVariables> & ({ variables: GetDocumentsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetDocumentsQuery, GetDocumentsQueryVariables>(GetDocumentsDocument, options);
      }
export function useGetDocumentsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetDocumentsQuery, GetDocumentsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetDocumentsQuery, GetDocumentsQueryVariables>(GetDocumentsDocument, options);
        }
export function useGetDocumentsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetDocumentsQuery, GetDocumentsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetDocumentsQuery, GetDocumentsQueryVariables>(GetDocumentsDocument, options);
        }
export type GetDocumentsQueryHookResult = ReturnType<typeof useGetDocumentsQuery>;
export type GetDocumentsLazyQueryHookResult = ReturnType<typeof useGetDocumentsLazyQuery>;
export type GetDocumentsSuspenseQueryHookResult = ReturnType<typeof useGetDocumentsSuspenseQuery>;
export type GetDocumentsQueryResult = Apollo.QueryResult<GetDocumentsQuery, GetDocumentsQueryVariables>;
export const GetOwnedKeysDocument = gql`
    query GetOwnedKeys($token: String, $limit: Int!) {
  ownedKeys(token: $token, limit: $limit) {
    items {
      id
      name
      owner
      status
    }
    nextToken
  }
}
    `;

/**
 * __useGetOwnedKeysQuery__
 *
 * To run a query within a React component, call `useGetOwnedKeysQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetOwnedKeysQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetOwnedKeysQuery({
 *   variables: {
 *      token: // value for 'token'
 *      limit: // value for 'limit'
 *   },
 * });
 */
export function useGetOwnedKeysQuery(baseOptions: Apollo.QueryHookOptions<GetOwnedKeysQuery, GetOwnedKeysQueryVariables> & ({ variables: GetOwnedKeysQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetOwnedKeysQuery, GetOwnedKeysQueryVariables>(GetOwnedKeysDocument, options);
      }
export function useGetOwnedKeysLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetOwnedKeysQuery, GetOwnedKeysQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetOwnedKeysQuery, GetOwnedKeysQueryVariables>(GetOwnedKeysDocument, options);
        }
export function useGetOwnedKeysSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetOwnedKeysQuery, GetOwnedKeysQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetOwnedKeysQuery, GetOwnedKeysQueryVariables>(GetOwnedKeysDocument, options);
        }
export type GetOwnedKeysQueryHookResult = ReturnType<typeof useGetOwnedKeysQuery>;
export type GetOwnedKeysLazyQueryHookResult = ReturnType<typeof useGetOwnedKeysLazyQuery>;
export type GetOwnedKeysSuspenseQueryHookResult = ReturnType<typeof useGetOwnedKeysSuspenseQuery>;
export type GetOwnedKeysQueryResult = Apollo.QueryResult<GetOwnedKeysQuery, GetOwnedKeysQueryVariables>;
export const GetSecretDocument = gql`
    query GetSecret($sha: String!) {
  secret(sha: $sha)
}
    `;

/**
 * __useGetSecretQuery__
 *
 * To run a query within a React component, call `useGetSecretQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSecretQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSecretQuery({
 *   variables: {
 *      sha: // value for 'sha'
 *   },
 * });
 */
export function useGetSecretQuery(baseOptions: Apollo.QueryHookOptions<GetSecretQuery, GetSecretQueryVariables> & ({ variables: GetSecretQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSecretQuery, GetSecretQueryVariables>(GetSecretDocument, options);
      }
export function useGetSecretLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSecretQuery, GetSecretQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSecretQuery, GetSecretQueryVariables>(GetSecretDocument, options);
        }
export function useGetSecretSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetSecretQuery, GetSecretQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetSecretQuery, GetSecretQueryVariables>(GetSecretDocument, options);
        }
export type GetSecretQueryHookResult = ReturnType<typeof useGetSecretQuery>;
export type GetSecretLazyQueryHookResult = ReturnType<typeof useGetSecretLazyQuery>;
export type GetSecretSuspenseQueryHookResult = ReturnType<typeof useGetSecretSuspenseQuery>;
export type GetSecretQueryResult = Apollo.QueryResult<GetSecretQuery, GetSecretQueryVariables>;
export const GetRequirementSnapshotDocument = gql`
    query GetRequirementSnapshot($snapshotId: String) {
  requirementSnapshot(snapshotId: $snapshotId) {
    author
    createdAt
    id
    status {
      __typename
      ... on RequirementSnapshotStatusStarting {
        status
      }
      ... on RequirementSnapshotStatusFetching {
        completed
        total
      }
      ... on RequirementSnapshotStatusProcessing {
        completed
        total
      }
      ... on RequirementSnapshotStatusDone {
        status
      }
      ... on RequirementSnapshotStatusError {
        errorMessage
      }
      ... on RequirementSnapshotStatusDeleting {
        status
      }
    }
    downloadUrl
    tags {
      id
      text
      createdAt
    }
  }
}
    `;

/**
 * __useGetRequirementSnapshotQuery__
 *
 * To run a query within a React component, call `useGetRequirementSnapshotQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRequirementSnapshotQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRequirementSnapshotQuery({
 *   variables: {
 *      snapshotId: // value for 'snapshotId'
 *   },
 * });
 */
export function useGetRequirementSnapshotQuery(baseOptions?: Apollo.QueryHookOptions<GetRequirementSnapshotQuery, GetRequirementSnapshotQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRequirementSnapshotQuery, GetRequirementSnapshotQueryVariables>(GetRequirementSnapshotDocument, options);
      }
export function useGetRequirementSnapshotLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRequirementSnapshotQuery, GetRequirementSnapshotQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRequirementSnapshotQuery, GetRequirementSnapshotQueryVariables>(GetRequirementSnapshotDocument, options);
        }
export function useGetRequirementSnapshotSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetRequirementSnapshotQuery, GetRequirementSnapshotQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetRequirementSnapshotQuery, GetRequirementSnapshotQueryVariables>(GetRequirementSnapshotDocument, options);
        }
export type GetRequirementSnapshotQueryHookResult = ReturnType<typeof useGetRequirementSnapshotQuery>;
export type GetRequirementSnapshotLazyQueryHookResult = ReturnType<typeof useGetRequirementSnapshotLazyQuery>;
export type GetRequirementSnapshotSuspenseQueryHookResult = ReturnType<typeof useGetRequirementSnapshotSuspenseQuery>;
export type GetRequirementSnapshotQueryResult = Apollo.QueryResult<GetRequirementSnapshotQuery, GetRequirementSnapshotQueryVariables>;
export const GetRequirementSnapshotsDocument = gql`
    query GetRequirementSnapshots($token: String, $limit: Int!, $mineOnly: Boolean!, $statusFilter: RequirementSnapshotStatusFilter!, $first: DateTime!, $last: DateTime!) {
  requirementSnapshots(
    token: $token
    limit: $limit
    mineOnly: $mineOnly
    statusFilter: $statusFilter
    first: $first
    last: $last
  ) {
    items {
      author
      createdAt
      id
      status {
        __typename
        ... on RequirementSnapshotStatusStarting {
          status
        }
        ... on RequirementSnapshotStatusFetching {
          completed
          total
        }
        ... on RequirementSnapshotStatusProcessing {
          completed
          total
        }
        ... on RequirementSnapshotStatusDone {
          status
        }
        ... on RequirementSnapshotStatusError {
          errorMessage
        }
        ... on RequirementSnapshotStatusDeleting {
          status
        }
      }
      downloadUrl
      tags {
        id
        text
        createdAt
      }
    }
    nextToken
  }
}
    `;

/**
 * __useGetRequirementSnapshotsQuery__
 *
 * To run a query within a React component, call `useGetRequirementSnapshotsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRequirementSnapshotsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRequirementSnapshotsQuery({
 *   variables: {
 *      token: // value for 'token'
 *      limit: // value for 'limit'
 *      mineOnly: // value for 'mineOnly'
 *      statusFilter: // value for 'statusFilter'
 *      first: // value for 'first'
 *      last: // value for 'last'
 *   },
 * });
 */
export function useGetRequirementSnapshotsQuery(baseOptions: Apollo.QueryHookOptions<GetRequirementSnapshotsQuery, GetRequirementSnapshotsQueryVariables> & ({ variables: GetRequirementSnapshotsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRequirementSnapshotsQuery, GetRequirementSnapshotsQueryVariables>(GetRequirementSnapshotsDocument, options);
      }
export function useGetRequirementSnapshotsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRequirementSnapshotsQuery, GetRequirementSnapshotsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRequirementSnapshotsQuery, GetRequirementSnapshotsQueryVariables>(GetRequirementSnapshotsDocument, options);
        }
export function useGetRequirementSnapshotsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetRequirementSnapshotsQuery, GetRequirementSnapshotsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetRequirementSnapshotsQuery, GetRequirementSnapshotsQueryVariables>(GetRequirementSnapshotsDocument, options);
        }
export type GetRequirementSnapshotsQueryHookResult = ReturnType<typeof useGetRequirementSnapshotsQuery>;
export type GetRequirementSnapshotsLazyQueryHookResult = ReturnType<typeof useGetRequirementSnapshotsLazyQuery>;
export type GetRequirementSnapshotsSuspenseQueryHookResult = ReturnType<typeof useGetRequirementSnapshotsSuspenseQuery>;
export type GetRequirementSnapshotsQueryResult = Apollo.QueryResult<GetRequirementSnapshotsQuery, GetRequirementSnapshotsQueryVariables>;
export const GetSnapshotsWithTagDocument = gql`
    query GetSnapshotsWithTag($token: String, $limit: Int!, $tag: String, $first: DateTime!, $last: DateTime!) {
  snapshotsWithTag(
    token: $token
    limit: $limit
    tag: $tag
    first: $first
    last: $last
  ) {
    items {
      author
      createdAt
      id
      status {
        __typename
        ... on RequirementSnapshotStatusStarting {
          status
        }
        ... on RequirementSnapshotStatusFetching {
          completed
          total
        }
        ... on RequirementSnapshotStatusProcessing {
          completed
          total
        }
        ... on RequirementSnapshotStatusDone {
          status
        }
        ... on RequirementSnapshotStatusError {
          errorMessage
        }
        ... on RequirementSnapshotStatusDeleting {
          status
        }
      }
      downloadUrl
      tags {
        id
        text
        createdAt
      }
    }
    nextToken
  }
}
    `;

/**
 * __useGetSnapshotsWithTagQuery__
 *
 * To run a query within a React component, call `useGetSnapshotsWithTagQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSnapshotsWithTagQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSnapshotsWithTagQuery({
 *   variables: {
 *      token: // value for 'token'
 *      limit: // value for 'limit'
 *      tag: // value for 'tag'
 *      first: // value for 'first'
 *      last: // value for 'last'
 *   },
 * });
 */
export function useGetSnapshotsWithTagQuery(baseOptions: Apollo.QueryHookOptions<GetSnapshotsWithTagQuery, GetSnapshotsWithTagQueryVariables> & ({ variables: GetSnapshotsWithTagQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSnapshotsWithTagQuery, GetSnapshotsWithTagQueryVariables>(GetSnapshotsWithTagDocument, options);
      }
export function useGetSnapshotsWithTagLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSnapshotsWithTagQuery, GetSnapshotsWithTagQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSnapshotsWithTagQuery, GetSnapshotsWithTagQueryVariables>(GetSnapshotsWithTagDocument, options);
        }
export function useGetSnapshotsWithTagSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetSnapshotsWithTagQuery, GetSnapshotsWithTagQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetSnapshotsWithTagQuery, GetSnapshotsWithTagQueryVariables>(GetSnapshotsWithTagDocument, options);
        }
export type GetSnapshotsWithTagQueryHookResult = ReturnType<typeof useGetSnapshotsWithTagQuery>;
export type GetSnapshotsWithTagLazyQueryHookResult = ReturnType<typeof useGetSnapshotsWithTagLazyQuery>;
export type GetSnapshotsWithTagSuspenseQueryHookResult = ReturnType<typeof useGetSnapshotsWithTagSuspenseQuery>;
export type GetSnapshotsWithTagQueryResult = Apollo.QueryResult<GetSnapshotsWithTagQuery, GetSnapshotsWithTagQueryVariables>;
export const GetRequirementTagDocument = gql`
    query GetRequirementTag($tagId: String, $text: String) {
  requirementTag(tagId: $tagId, text: $text) {
    createdAt
    id
    text
  }
}
    `;

/**
 * __useGetRequirementTagQuery__
 *
 * To run a query within a React component, call `useGetRequirementTagQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRequirementTagQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRequirementTagQuery({
 *   variables: {
 *      tagId: // value for 'tagId'
 *      text: // value for 'text'
 *   },
 * });
 */
export function useGetRequirementTagQuery(baseOptions?: Apollo.QueryHookOptions<GetRequirementTagQuery, GetRequirementTagQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRequirementTagQuery, GetRequirementTagQueryVariables>(GetRequirementTagDocument, options);
      }
export function useGetRequirementTagLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRequirementTagQuery, GetRequirementTagQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRequirementTagQuery, GetRequirementTagQueryVariables>(GetRequirementTagDocument, options);
        }
export function useGetRequirementTagSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetRequirementTagQuery, GetRequirementTagQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetRequirementTagQuery, GetRequirementTagQueryVariables>(GetRequirementTagDocument, options);
        }
export type GetRequirementTagQueryHookResult = ReturnType<typeof useGetRequirementTagQuery>;
export type GetRequirementTagLazyQueryHookResult = ReturnType<typeof useGetRequirementTagLazyQuery>;
export type GetRequirementTagSuspenseQueryHookResult = ReturnType<typeof useGetRequirementTagSuspenseQuery>;
export type GetRequirementTagQueryResult = Apollo.QueryResult<GetRequirementTagQuery, GetRequirementTagQueryVariables>;
export const GetRequirementTagsDocument = gql`
    query GetRequirementTags($token: String, $limit: Int!, $prefix: String) {
  requirementTags(token: $token, limit: $limit, prefix: $prefix) {
    items {
      createdAt
      id
      text
    }
    nextToken
  }
}
    `;

/**
 * __useGetRequirementTagsQuery__
 *
 * To run a query within a React component, call `useGetRequirementTagsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRequirementTagsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRequirementTagsQuery({
 *   variables: {
 *      token: // value for 'token'
 *      limit: // value for 'limit'
 *      prefix: // value for 'prefix'
 *   },
 * });
 */
export function useGetRequirementTagsQuery(baseOptions: Apollo.QueryHookOptions<GetRequirementTagsQuery, GetRequirementTagsQueryVariables> & ({ variables: GetRequirementTagsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRequirementTagsQuery, GetRequirementTagsQueryVariables>(GetRequirementTagsDocument, options);
      }
export function useGetRequirementTagsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRequirementTagsQuery, GetRequirementTagsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRequirementTagsQuery, GetRequirementTagsQueryVariables>(GetRequirementTagsDocument, options);
        }
export function useGetRequirementTagsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetRequirementTagsQuery, GetRequirementTagsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetRequirementTagsQuery, GetRequirementTagsQueryVariables>(GetRequirementTagsDocument, options);
        }
export type GetRequirementTagsQueryHookResult = ReturnType<typeof useGetRequirementTagsQuery>;
export type GetRequirementTagsLazyQueryHookResult = ReturnType<typeof useGetRequirementTagsLazyQuery>;
export type GetRequirementTagsSuspenseQueryHookResult = ReturnType<typeof useGetRequirementTagsSuspenseQuery>;
export type GetRequirementTagsQueryResult = Apollo.QueryResult<GetRequirementTagsQuery, GetRequirementTagsQueryVariables>;
export const GetVerificationDocument = gql`
    query GetVerification($verificationId: String) {
  verification(verificationId: $verificationId) {
    createdAt
    createdBy
    documentId
    documentVersion
    id
    rankingStatus {
      __typename
      ... on TaskStatusDone {
        status
      }
      ... on TaskStatusError {
        errorMessage
      }
      ... on TaskStatusPending {
        status
      }
      ... on TaskStatusProcessing {
        completed
        total
      }
    }
    reportingStatus {
      __typename
      ... on TaskStatusDone {
        status
      }
      ... on TaskStatusError {
        errorMessage
      }
      ... on TaskStatusPending {
        status
      }
      ... on TaskStatusProcessing {
        completed
        total
      }
    }
    snapshotId
    status {
      __typename
      ... on VerificationStatusDeleting {
        status
      }
      ... on VerificationStatusDone {
        status
      }
      ... on VerificationStatusError {
        status
      }
      ... on VerificationStatusRunning {
        status
      }
    }
    verifyingStatus {
      __typename
      ... on TaskStatusDone {
        status
      }
      ... on TaskStatusError {
        errorMessage
      }
      ... on TaskStatusPending {
        status
      }
      ... on TaskStatusProcessing {
        completed
        total
      }
    }
  }
}
    `;

/**
 * __useGetVerificationQuery__
 *
 * To run a query within a React component, call `useGetVerificationQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVerificationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVerificationQuery({
 *   variables: {
 *      verificationId: // value for 'verificationId'
 *   },
 * });
 */
export function useGetVerificationQuery(baseOptions?: Apollo.QueryHookOptions<GetVerificationQuery, GetVerificationQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetVerificationQuery, GetVerificationQueryVariables>(GetVerificationDocument, options);
      }
export function useGetVerificationLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetVerificationQuery, GetVerificationQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetVerificationQuery, GetVerificationQueryVariables>(GetVerificationDocument, options);
        }
export function useGetVerificationSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetVerificationQuery, GetVerificationQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetVerificationQuery, GetVerificationQueryVariables>(GetVerificationDocument, options);
        }
export type GetVerificationQueryHookResult = ReturnType<typeof useGetVerificationQuery>;
export type GetVerificationLazyQueryHookResult = ReturnType<typeof useGetVerificationLazyQuery>;
export type GetVerificationSuspenseQueryHookResult = ReturnType<typeof useGetVerificationSuspenseQuery>;
export type GetVerificationQueryResult = Apollo.QueryResult<GetVerificationQuery, GetVerificationQueryVariables>;
export const GetVerificationsDocument = gql`
    query GetVerifications($token: String, $limit: Int!, $first: DateTime!, $last: DateTime!, $mineOnly: Boolean!, $statusFilter: VerificationStatusFilter!) {
  verifications(
    token: $token
    limit: $limit
    first: $first
    last: $last
    mineOnly: $mineOnly
    statusFilter: $statusFilter
  ) {
    items {
      createdAt
      createdBy
      documentId
      documentVersion
      id
      rankingStatus {
        __typename
        ... on TaskStatusDone {
          status
        }
        ... on TaskStatusError {
          errorMessage
        }
        ... on TaskStatusPending {
          status
        }
        ... on TaskStatusProcessing {
          completed
          total
        }
      }
      reportingStatus {
        __typename
        ... on TaskStatusDone {
          status
        }
        ... on TaskStatusError {
          errorMessage
        }
        ... on TaskStatusPending {
          status
        }
        ... on TaskStatusProcessing {
          completed
          total
        }
      }
      snapshotId
      status {
        __typename
        ... on VerificationStatusDeleting {
          status
        }
        ... on VerificationStatusDone {
          status
        }
        ... on VerificationStatusError {
          status
        }
        ... on VerificationStatusRunning {
          status
        }
      }
      verifyingStatus {
        __typename
        ... on TaskStatusDone {
          status
        }
        ... on TaskStatusError {
          errorMessage
        }
        ... on TaskStatusPending {
          status
        }
        ... on TaskStatusProcessing {
          completed
          total
        }
      }
    }
    nextToken
  }
}
    `;

/**
 * __useGetVerificationsQuery__
 *
 * To run a query within a React component, call `useGetVerificationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVerificationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVerificationsQuery({
 *   variables: {
 *      token: // value for 'token'
 *      limit: // value for 'limit'
 *      first: // value for 'first'
 *      last: // value for 'last'
 *      mineOnly: // value for 'mineOnly'
 *      statusFilter: // value for 'statusFilter'
 *   },
 * });
 */
export function useGetVerificationsQuery(baseOptions: Apollo.QueryHookOptions<GetVerificationsQuery, GetVerificationsQueryVariables> & ({ variables: GetVerificationsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetVerificationsQuery, GetVerificationsQueryVariables>(GetVerificationsDocument, options);
      }
export function useGetVerificationsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetVerificationsQuery, GetVerificationsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetVerificationsQuery, GetVerificationsQueryVariables>(GetVerificationsDocument, options);
        }
export function useGetVerificationsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetVerificationsQuery, GetVerificationsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetVerificationsQuery, GetVerificationsQueryVariables>(GetVerificationsDocument, options);
        }
export type GetVerificationsQueryHookResult = ReturnType<typeof useGetVerificationsQuery>;
export type GetVerificationsLazyQueryHookResult = ReturnType<typeof useGetVerificationsLazyQuery>;
export type GetVerificationsSuspenseQueryHookResult = ReturnType<typeof useGetVerificationsSuspenseQuery>;
export type GetVerificationsQueryResult = Apollo.QueryResult<GetVerificationsQuery, GetVerificationsQueryVariables>;
export const GetVerificationsWithTagDocument = gql`
    query GetVerificationsWithTag($token: String, $limit: Int!, $tag: String, $first: DateTime!, $last: DateTime!) {
  verificationsWithTag(
    token: $token
    limit: $limit
    tag: $tag
    first: $first
    last: $last
  ) {
    items {
      createdAt
      createdBy
      documentId
      documentVersion
      id
      rankingStatus {
        __typename
        ... on TaskStatusDone {
          status
        }
        ... on TaskStatusError {
          errorMessage
        }
        ... on TaskStatusPending {
          status
        }
        ... on TaskStatusProcessing {
          completed
          total
        }
      }
      reportingStatus {
        __typename
        ... on TaskStatusDone {
          status
        }
        ... on TaskStatusError {
          errorMessage
        }
        ... on TaskStatusPending {
          status
        }
        ... on TaskStatusProcessing {
          completed
          total
        }
      }
      snapshotId
      status {
        __typename
        ... on VerificationStatusDeleting {
          status
        }
        ... on VerificationStatusDone {
          status
        }
        ... on VerificationStatusError {
          status
        }
        ... on VerificationStatusRunning {
          status
        }
      }
      verifyingStatus {
        __typename
        ... on TaskStatusDone {
          status
        }
        ... on TaskStatusError {
          errorMessage
        }
        ... on TaskStatusPending {
          status
        }
        ... on TaskStatusProcessing {
          completed
          total
        }
      }
    }
    nextToken
  }
}
    `;

/**
 * __useGetVerificationsWithTagQuery__
 *
 * To run a query within a React component, call `useGetVerificationsWithTagQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVerificationsWithTagQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVerificationsWithTagQuery({
 *   variables: {
 *      token: // value for 'token'
 *      limit: // value for 'limit'
 *      tag: // value for 'tag'
 *      first: // value for 'first'
 *      last: // value for 'last'
 *   },
 * });
 */
export function useGetVerificationsWithTagQuery(baseOptions: Apollo.QueryHookOptions<GetVerificationsWithTagQuery, GetVerificationsWithTagQueryVariables> & ({ variables: GetVerificationsWithTagQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetVerificationsWithTagQuery, GetVerificationsWithTagQueryVariables>(GetVerificationsWithTagDocument, options);
      }
export function useGetVerificationsWithTagLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetVerificationsWithTagQuery, GetVerificationsWithTagQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetVerificationsWithTagQuery, GetVerificationsWithTagQueryVariables>(GetVerificationsWithTagDocument, options);
        }
export function useGetVerificationsWithTagSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetVerificationsWithTagQuery, GetVerificationsWithTagQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetVerificationsWithTagQuery, GetVerificationsWithTagQueryVariables>(GetVerificationsWithTagDocument, options);
        }
export type GetVerificationsWithTagQueryHookResult = ReturnType<typeof useGetVerificationsWithTagQuery>;
export type GetVerificationsWithTagLazyQueryHookResult = ReturnType<typeof useGetVerificationsWithTagLazyQuery>;
export type GetVerificationsWithTagSuspenseQueryHookResult = ReturnType<typeof useGetVerificationsWithTagSuspenseQuery>;
export type GetVerificationsWithTagQueryResult = Apollo.QueryResult<GetVerificationsWithTagQuery, GetVerificationsWithTagQueryVariables>;
export const GetVerificationTagDocument = gql`
    query GetVerificationTag($tagId: String, $text: String) {
  verificationTag(tagId: $tagId, text: $text) {
    createdAt
    id
    text
  }
}
    `;

/**
 * __useGetVerificationTagQuery__
 *
 * To run a query within a React component, call `useGetVerificationTagQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVerificationTagQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVerificationTagQuery({
 *   variables: {
 *      tagId: // value for 'tagId'
 *      text: // value for 'text'
 *   },
 * });
 */
export function useGetVerificationTagQuery(baseOptions?: Apollo.QueryHookOptions<GetVerificationTagQuery, GetVerificationTagQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetVerificationTagQuery, GetVerificationTagQueryVariables>(GetVerificationTagDocument, options);
      }
export function useGetVerificationTagLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetVerificationTagQuery, GetVerificationTagQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetVerificationTagQuery, GetVerificationTagQueryVariables>(GetVerificationTagDocument, options);
        }
export function useGetVerificationTagSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetVerificationTagQuery, GetVerificationTagQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetVerificationTagQuery, GetVerificationTagQueryVariables>(GetVerificationTagDocument, options);
        }
export type GetVerificationTagQueryHookResult = ReturnType<typeof useGetVerificationTagQuery>;
export type GetVerificationTagLazyQueryHookResult = ReturnType<typeof useGetVerificationTagLazyQuery>;
export type GetVerificationTagSuspenseQueryHookResult = ReturnType<typeof useGetVerificationTagSuspenseQuery>;
export type GetVerificationTagQueryResult = Apollo.QueryResult<GetVerificationTagQuery, GetVerificationTagQueryVariables>;
export const GetVerificationTagsDocument = gql`
    query GetVerificationTags($token: String, $limit: Int!, $prefix: String) {
  verificationTags(token: $token, limit: $limit, prefix: $prefix) {
    items {
      createdAt
      id
      text
    }
    nextToken
  }
}
    `;

/**
 * __useGetVerificationTagsQuery__
 *
 * To run a query within a React component, call `useGetVerificationTagsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVerificationTagsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVerificationTagsQuery({
 *   variables: {
 *      token: // value for 'token'
 *      limit: // value for 'limit'
 *      prefix: // value for 'prefix'
 *   },
 * });
 */
export function useGetVerificationTagsQuery(baseOptions: Apollo.QueryHookOptions<GetVerificationTagsQuery, GetVerificationTagsQueryVariables> & ({ variables: GetVerificationTagsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetVerificationTagsQuery, GetVerificationTagsQueryVariables>(GetVerificationTagsDocument, options);
      }
export function useGetVerificationTagsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetVerificationTagsQuery, GetVerificationTagsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetVerificationTagsQuery, GetVerificationTagsQueryVariables>(GetVerificationTagsDocument, options);
        }
export function useGetVerificationTagsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetVerificationTagsQuery, GetVerificationTagsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetVerificationTagsQuery, GetVerificationTagsQueryVariables>(GetVerificationTagsDocument, options);
        }
export type GetVerificationTagsQueryHookResult = ReturnType<typeof useGetVerificationTagsQuery>;
export type GetVerificationTagsLazyQueryHookResult = ReturnType<typeof useGetVerificationTagsLazyQuery>;
export type GetVerificationTagsSuspenseQueryHookResult = ReturnType<typeof useGetVerificationTagsSuspenseQuery>;
export type GetVerificationTagsQueryResult = Apollo.QueryResult<GetVerificationTagsQuery, GetVerificationTagsQueryVariables>;
export const GetVerificationReportUrlDocument = gql`
    query GetVerificationReportUrl($verificationId: String) {
  verificationReportUrl(verificationId: $verificationId)
}
    `;

/**
 * __useGetVerificationReportUrlQuery__
 *
 * To run a query within a React component, call `useGetVerificationReportUrlQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVerificationReportUrlQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVerificationReportUrlQuery({
 *   variables: {
 *      verificationId: // value for 'verificationId'
 *   },
 * });
 */
export function useGetVerificationReportUrlQuery(baseOptions?: Apollo.QueryHookOptions<GetVerificationReportUrlQuery, GetVerificationReportUrlQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetVerificationReportUrlQuery, GetVerificationReportUrlQueryVariables>(GetVerificationReportUrlDocument, options);
      }
export function useGetVerificationReportUrlLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetVerificationReportUrlQuery, GetVerificationReportUrlQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetVerificationReportUrlQuery, GetVerificationReportUrlQueryVariables>(GetVerificationReportUrlDocument, options);
        }
export function useGetVerificationReportUrlSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetVerificationReportUrlQuery, GetVerificationReportUrlQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetVerificationReportUrlQuery, GetVerificationReportUrlQueryVariables>(GetVerificationReportUrlDocument, options);
        }
export type GetVerificationReportUrlQueryHookResult = ReturnType<typeof useGetVerificationReportUrlQuery>;
export type GetVerificationReportUrlLazyQueryHookResult = ReturnType<typeof useGetVerificationReportUrlLazyQuery>;
export type GetVerificationReportUrlSuspenseQueryHookResult = ReturnType<typeof useGetVerificationReportUrlSuspenseQuery>;
export type GetVerificationReportUrlQueryResult = Apollo.QueryResult<GetVerificationReportUrlQuery, GetVerificationReportUrlQueryVariables>;