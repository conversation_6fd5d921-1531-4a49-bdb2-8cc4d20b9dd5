query GetAccessibleKeys($token: String, $limit: Int!) {
    accessibleKeys(token: $token, limit: $limit) {
        items {
        id
        name
        owner
        status
        }
        nextToken
    }
}

query GetAccessors($token: String, $limit: Int!, $sha: String!) {
    accessors(token: $token, limit: $limit, sha: $sha) {
        items {
        keyID
        accessorEmail
        }
        nextToken
    }
}

query GetApiKey($sha: String!) {
    apiKey(sha: $sha) {
        id
        name
        owner
        status
    }
}

query GetDocument($documentId: String!, $version: String!) {
    document(documentId: $documentId, version: $version) {
        documentId
        endTime {
            value
        }
        startTime
        status {
            __typename
            ... on DocumentStatusStarted {
                status
            }
            ... on DocumentStatusUploaded {
                status
            }
            ... on DocumentStatusSplitting {
                status
            }
            ... on DocumentStatusAnnotating {
                current
                total
            }
            ... on DocumentStatusEmbedding {
                current
                total
            }
            ... on DocumentStatusComplete {
                status
            }
            ... on DocumentStatusError {
                errorMessage
            }
        }
        updatedBy
        version
        uploadUrl {
            value
        }
    }
}

query GetDocuments($token: String, $limit: Int!, $mineOnly: Boolean!, $status: DocumentStatusFilter!, $first: DateTime!, $last: DateTime!) {
    documents(token: $token, limit: $limit, mineOnly: $mineOnly, status: $status, first: $first, last: $last) {
        items {
            documentId
            endTime {
                value
            }
            startTime
            status {
                __typename
                ... on DocumentStatusStarted {
                    status
                }
                ... on DocumentStatusUploaded {
                    status
                }
                ... on DocumentStatusSplitting {
                    status
                }
                ... on DocumentStatusAnnotating {
                    current
                    total
                }
                ... on DocumentStatusEmbedding {
                    current
                    total
                }
                ... on DocumentStatusComplete {
                    status
                }
                ... on DocumentStatusError {
                    errorMessage
                }
            }
            updatedBy
            version
            uploadUrl {
                value
            }
        }
        nextToken
    }
}

query GetOwnedKeys($token: String, $limit: Int!) {
    ownedKeys(token: $token, limit: $limit) {
        items {
            id
            name
            owner
            status
        }
        nextToken
    }
}

query GetSecret($sha: String!) {
    secret(sha: $sha)
}

query GetRequirementSnapshot($snapshotId: String) {
    requirementSnapshot(snapshotId: $snapshotId) {
        author
        createdAt
        id
        status {
            __typename
            ... on RequirementSnapshotStatusStarting {
                status
            }
            ... on RequirementSnapshotStatusFetching {
                completed
                total
            }
            ... on RequirementSnapshotStatusProcessing {
                completed
                total
            }
            ... on RequirementSnapshotStatusDone {
                status
            }
            ... on RequirementSnapshotStatusError {
                errorMessage
            }
            ... on RequirementSnapshotStatusDeleting {
                status
            }
        }
        downloadUrl
        tags {
            id
            text
            createdAt
        }
    }
}

query GetRequirementSnapshots($token: String, $limit: Int!, $mineOnly: Boolean!, $statusFilter: RequirementSnapshotStatusFilter!, $first: DateTime!, $last: DateTime!) {
    requirementSnapshots(token: $token, limit: $limit, mineOnly: $mineOnly, statusFilter: $statusFilter, first: $first, last: $last) {
        items {
            author
            createdAt
            id
            status {
                __typename
                ... on RequirementSnapshotStatusStarting {
                    status
                }
                ... on RequirementSnapshotStatusFetching {
                    completed
                    total
                }
                ... on RequirementSnapshotStatusProcessing {
                    completed
                    total
                }
                ... on RequirementSnapshotStatusDone {
                    status
                }
                ... on RequirementSnapshotStatusError {
                    errorMessage
                }
                ... on RequirementSnapshotStatusDeleting {
                    status
                }
            }
            downloadUrl
            tags {
                id
                text
                createdAt
            }
        }
        nextToken
    }
}

query GetSnapshotsWithTag($token: String, $limit: Int!, $tag: String, $first: DateTime!, $last: DateTime!) {
    snapshotsWithTag(token: $token, limit: $limit, tag: $tag, first: $first, last: $last) {
        items {
            author
            createdAt
            id
            status {
                __typename
                ... on RequirementSnapshotStatusStarting {
                    status
                }
                ... on RequirementSnapshotStatusFetching {
                    completed
                    total
                }
                ... on RequirementSnapshotStatusProcessing {
                    completed
                    total
                }
                ... on RequirementSnapshotStatusDone {
                    status
                }
                ... on RequirementSnapshotStatusError {
                    errorMessage
                }
                ... on RequirementSnapshotStatusDeleting {
                    status
                }
            }
            downloadUrl
            tags {
                id
                text
                createdAt
            }
        }
        nextToken
    }
}

query GetRequirementTag($tagId: String, $text: String) {
    requirementTag(tagId: $tagId, text: $text) {
        createdAt
        id
        text
    }
}

query GetRequirementTags($token: String, $limit: Int!, $prefix: String) {
    requirementTags(token: $token, limit: $limit, prefix: $prefix) {
        items {
            createdAt
            id
            text
        }
        nextToken
    }
}

query GetVerification($verificationId: String) {
    verification(verificationId: $verificationId) {
        createdAt
        createdBy
        documentId
        documentVersion
        id
        rankingStatus {
            __typename
            ... on TaskStatusDone {
                status
            }
            ... on TaskStatusError {
                errorMessage
            }
            ... on TaskStatusPending {
                status
            }
            ... on TaskStatusProcessing {
                completed
                total
            }
        }
        reportingStatus {
            __typename
            ... on TaskStatusDone {
                status
            }
            ... on TaskStatusError {
                errorMessage
            }
            ... on TaskStatusPending {
                status
            }
            ... on TaskStatusProcessing {
                completed
                total
            }
        }
        snapshotId
        status {
            __typename
            ... on VerificationStatusDeleting {
                status
            }
            ... on VerificationStatusDone {
                status
            }
            ... on VerificationStatusError {
                status
            }
            ... on VerificationStatusRunning {
                status
            }
        }
        verifyingStatus {
            __typename
            ... on TaskStatusDone {
                status
            }
            ... on TaskStatusError {
                errorMessage
            }
            ... on TaskStatusPending {
                status
            }
            ... on TaskStatusProcessing {
                completed
                total
            }
        }
    }
}

query GetVerifications($token: String, $limit: Int!, $first: DateTime!, $last: DateTime!, $mineOnly: Boolean!, $statusFilter: VerificationStatusFilter!) {
    verifications(token: $token, limit: $limit, first: $first, last: $last, mineOnly: $mineOnly, statusFilter: $statusFilter) {
        items {
            createdAt
            createdBy
            documentId
            documentVersion
            id
            rankingStatus {
                __typename
                ... on TaskStatusDone {
                    status
                }
                ... on TaskStatusError {
                    errorMessage
                }
                ... on TaskStatusPending {
                    status
                }
                ... on TaskStatusProcessing {
                    completed
                    total
                }
            }
            reportingStatus {
                __typename
                ... on TaskStatusDone {
                    status
                }
                ... on TaskStatusError {
                    errorMessage
                }
                ... on TaskStatusPending {
                    status
                }
                ... on TaskStatusProcessing {
                    completed
                    total
                }
            }
            snapshotId
            status {
                __typename
                ... on VerificationStatusDeleting {
                    status
                }
                ... on VerificationStatusDone {
                    status
                }
                ... on VerificationStatusError {
                    status
                }
                ... on VerificationStatusRunning {
                    status
                }
            }
            verifyingStatus {
                __typename
                ... on TaskStatusDone {
                    status
                }
                ... on TaskStatusError {
                    errorMessage
                }
                ... on TaskStatusPending {
                    status
                }
                ... on TaskStatusProcessing {
                    completed
                    total
                }
            }
        }
        nextToken
    }
}

query GetVerificationsWithTag($token: String, $limit: Int!, $tag: String, $first: DateTime!, $last: DateTime!) {
    verificationsWithTag(token: $token, limit: $limit, tag: $tag, first: $first, last: $last) {
        items {
            createdAt
            createdBy
            documentId
            documentVersion
            id
            rankingStatus {
                __typename
                ... on TaskStatusDone {
                    status
                }
                ... on TaskStatusError {
                    errorMessage
                }
                ... on TaskStatusPending {
                    status
                }
                ... on TaskStatusProcessing {
                    completed
                    total
                }
            }
            reportingStatus {
                __typename
                ... on TaskStatusDone {
                    status
                }
                ... on TaskStatusError {
                    errorMessage
                }
                ... on TaskStatusPending {
                    status
                }
                ... on TaskStatusProcessing {
                    completed
                    total
                }
            }
            snapshotId
            status {
                __typename
                ... on VerificationStatusDeleting {
                    status
                }
                ... on VerificationStatusDone {
                    status
                }
                ... on VerificationStatusError {
                    status
                }
                ... on VerificationStatusRunning {
                    status
                }
            }
            verifyingStatus {
                __typename
                ... on TaskStatusDone {
                    status
                }
                ... on TaskStatusError {
                    errorMessage
                }
                ... on TaskStatusPending {
                    status
                }
                ... on TaskStatusProcessing {
                    completed
                    total
                }
            }
        }
        nextToken
    }
}

query GetVerificationTag($tagId: String, $text: String) {
    verificationTag(tagId: $tagId, text: $text) {
        createdAt
        id
        text
    }
}

query GetVerificationTags($token: String, $limit: Int!, $prefix: String) {
    verificationTags(token: $token, limit: $limit, prefix: $prefix) {
        items {
            createdAt
            id
            text
        }
        nextToken
    }
}

query GetVerificationReportUrl($verificationId: String) {
    verificationReportUrl(verificationId: $verificationId)
}
