import React, { useEffect, useState, useMemo } from 'react';
import {
    <PERSON><PERSON>,
    But<PERSON>,
    Badge,
    Form,
    Alert,
    Spinner,
    Dropdown,
    InputGroup,
} from 'react-bootstrap';
import { 
    useGetRequirementTagsLazyQuery,
    useTagRequirementSnapshotMutation,
    useUntagRequirementSnapshotMutation,
    type RequirementSnapshot
} from '../../../gql/graphql';

interface Tag {
    id: string;
    text: string;
    createdAt?: string;
}

interface TagSnapshotModalProps {
    show: boolean;
    onHide: () => void;
    snapshot: RequirementSnapshot | null;
    onTagsUpdated?: () => void;
}

const TagSnapshotModal: React.FC<TagSnapshotModalProps> = ({
    show,
    onHide,
    snapshot,
    onTagsUpdated,
}) => {
    const [availableTags, setAvailableTags] = useState<Tag[]>([]);
    const [snapshotTags, setSnapshotTags] = useState<Tag[]>([]);
    const [message, setMessage] = useState<string | null>(null);
    const [messageType, setMessageType] = useState<'success' | 'danger' | null>(null);
    const [searchFilter, setSearchFilter] = useState<string>('');
    const [showDropdown, setShowDropdown] = useState<boolean>(false);

    const TOKENLIMIT = 1000;

    // GraphQL hooks
    const [fetchTags, { loading: tagsLoading, error: tagsError }] = useGetRequirementTagsLazyQuery();
    const [tagSnapshot, { loading: tagLoading }] = useTagRequirementSnapshotMutation();
    const [untagSnapshot, { loading: untagLoading }] = useUntagRequirementSnapshotMutation();

    // Load available tags when modal opens
    useEffect(() => {
        if (show) {
            fetchTags({
                variables: {
                    token: "",
                    limit: TOKENLIMIT,
                    prefix: ""
                }
            }).then((result) => {
                if (result.data?.requirementTags?.items) {
                    setAvailableTags(result.data.requirementTags.items.map(tag => ({
                        id: tag?.id || '',
                        text: tag?.text || '',
                        createdAt: tag?.createdAt
                    })));
                }
            });
        }
    }, [show, fetchTags]);

    // Set snapshot tags when snapshot changes
    useEffect(() => {
        if (snapshot?.tags) {
            setSnapshotTags(snapshot.tags.map(tag => ({
                id: tag?.id || '',
                text: tag?.text || '',
                createdAt: tag?.createdAt
            })));
        } else {
            setSnapshotTags([]);
        }
    }, [snapshot]);

    // Reset search filter when modal opens/closes
    useEffect(() => {
        if (!show) {
            setSearchFilter('');
            setShowDropdown(false);
        }
    }, [show]);

    // Filter available tags based on search and exclude already applied tags
    const filteredAvailableTags = useMemo(() => {
        return availableTags.filter(tag => {
            // Always exclude already applied tags
            const notAlreadyApplied = !snapshotTags.some(appliedTag => appliedTag.id === tag.id);

            // If no search filter, show all available tags
            if (!searchFilter.trim()) {
                return notAlreadyApplied;
            }

            // If there's a search filter, match against it
            const matchesSearch = tag.text.toLowerCase().includes(searchFilter.toLowerCase());
            return matchesSearch && notAlreadyApplied;
        });
    }, [availableTags, searchFilter, snapshotTags]);

    // Auto-manage dropdown visibility - show when focused or has content
    useEffect(() => {
        // Dropdown stays open when there are available tags to show
        if (filteredAvailableTags.length > 0) {
            // Keep dropdown open if there are tags to display
        }
    }, [searchFilter, filteredAvailableTags]);

    const showMessage = (msg: string, type: 'success' | 'danger') => {
        setMessage(msg);
        setMessageType(type);
        setTimeout(() => {
            setMessage(null);
            setMessageType(null);
        }, 3000);
    };

    const handleTagSnapshot = async (tag: Tag) => {
        if (!snapshot?.id) return;

        try {
            await tagSnapshot({
                variables: {
                    snapshotId: snapshot.id,
                    tagId: tag.id,
                    tagText: tag.text
                }
            });

            // Add tag to snapshot tags if not already present
            if (!snapshotTags.find(t => t.id === tag.id)) {
                setSnapshotTags(prev => [...prev, tag]);
            }

            showMessage(`Tagged with "${tag.text}"`, 'success');
            onTagsUpdated?.();
        } catch (error) {
            showMessage('Failed to tag snapshot', 'danger');
            console.error('Error tagging snapshot:', error);
        }
    };

    const handleUntagSnapshot = async (tag: Tag) => {
        if (!snapshot?.id) return;

        try {
            await untagSnapshot({
                variables: {
                    snapshotId: snapshot.id,
                    tagId: tag.id
                }
            });

            // Remove tag from snapshot tags
            setSnapshotTags(prev => prev.filter(t => t.id !== tag.id));

            showMessage(`Removed tag "${tag.text}"`, 'success');
            onTagsUpdated?.();
        } catch (error) {
            showMessage('Failed to remove tag', 'danger');
            console.error('Error removing tag:', error);
        }
    };



    const handleClose = () => {
        setMessage(null);
        setMessageType(null);
        setSearchFilter('');
        setShowDropdown(false);
        onHide();
    };

    return (
        <Modal show={show} onHide={handleClose} size="lg" centered>
            <Modal.Header closeButton>
                <Modal.Title>
                    Manage Tags for Snapshot: <br></br> {snapshot?.id}
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {message && (
                    <Alert variant={messageType || 'info'} className="mb-3">
                        {message}
                    </Alert>
                )}

                {/* Current Tags */}
                <div className="mb-4">
                    <h6>Current Tags:</h6>
                    {snapshotTags.length === 0 ? (
                        <p className="text-muted">No tags applied</p>
                    ) : (
                        <div>
                            {snapshotTags.map(tag => (
                                <Badge 
                                    key={tag.id} 
                                    bg="secondary" 
                                    className="me-2 mb-2 d-inline-flex align-items-center"
                                >
                                    {tag.text}
                                    <Button
                                        variant="dark"
                                        size="sm"
                                        className="p-0 ms-2 text-white"
                                        onClick={() => handleUntagSnapshot(tag)}
                                        disabled={untagLoading}
                                        style={{ fontSize: '20px', lineHeight: 1 }}
                                    >
                                        ×
                                    </Button>
                                </Badge>
                            ))}
                        </div>
                    )}
                </div>

                {/* Add Tags Section */}
                <div>
                    <h6>Add Tags:</h6>
                    {tagsLoading ? (
                        <div className="text-center">
                            <Spinner animation="border" size="sm" />
                            <span className="ms-2">Loading tags...</span>
                        </div>
                    ) : tagsError ? (
                        <Alert variant="danger">
                            Failed to load tags: {tagsError.message}
                        </Alert>
                    ) : (
                        <Dropdown
                            show={showDropdown}
                            onToggle={setShowDropdown}
                            autoClose="outside"
                        >
                            <InputGroup>
                                <Form.Control
                                    type="text"
                                    placeholder="Search tags or select from list..."
                                    value={searchFilter}
                                    onChange={(e) => {
                                        setSearchFilter(e.target.value);
                                        if (!showDropdown) {
                                            setShowDropdown(true);
                                        }
                                    }}
                                    onFocus={() => setShowDropdown(true)}
                                    onKeyDown={(e) => {
                                        if (e.key === 'Escape') {
                                            setShowDropdown(false);
                                        }
                                    }}
                                />
                                <Dropdown.Toggle
                                    variant="outline-secondary"
                                    id="dropdown-basic"
                                    style={{ borderLeft: 'none' }}
                                >
                                    {filteredAvailableTags.length}
                                </Dropdown.Toggle>
                            </InputGroup>

                            <Dropdown.Menu
                                show={showDropdown && filteredAvailableTags.length > 0}
                                style={{
                                    width: '100%',
                                    maxHeight: '200px',
                                    overflowY: 'auto',
                                    display: showDropdown && filteredAvailableTags.length > 0 ? 'block' : 'none'
                                }}
                            >
                                {filteredAvailableTags.length === 0 ? (
                                    <Dropdown.Item disabled>
                                        {searchFilter ? `No tags match "${searchFilter}"` : 'No available tags'}
                                    </Dropdown.Item>
                                ) : (
                                    filteredAvailableTags.map(tag => (
                                        <Dropdown.Item
                                            key={tag.id}
                                            onClick={() => {
                                                handleTagSnapshot(tag);
                                                setShowDropdown(false);
                                                setSearchFilter('');
                                            }}
                                            disabled={tagLoading}
                                            className="d-flex justify-content-between align-items-center"
                                        >
                                            <span>{tag.text}</span>
                                            <Badge bg="primary" className="small">Add</Badge>
                                        </Dropdown.Item>
                                    ))
                                )}
                            </Dropdown.Menu>
                        </Dropdown>
                    )}
                </div>
            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={handleClose}>
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default TagSnapshotModal;
