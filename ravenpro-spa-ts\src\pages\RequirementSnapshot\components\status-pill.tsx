import React from "react";
import { Badge, ProgressBar } from "react-bootstrap";
import type { RequirementSnapshotStatus } from "../../../gql/graphql";

interface StatusPillProps {
  status: RequirementSnapshotStatus;
}

export const StatusPill: React.FC<StatusPillProps> = ({ status }) => {
  switch (status.__typename) {
    case "RequirementSnapshotStatusStarting":
      return <Badge bg="primary">{status.status ?? "Starting"}</Badge>;

    case "RequirementSnapshotStatusFetching":
      return (
        <>
          <Badge bg="primary">Fetching</Badge>
          <ProgressBar
            now={(status.completed / status.total) * 100}
            label={`${status.completed} / ${status.total}`}
            striped
            animated
            className="mt-1"
          />
        </>
      );

    case "RequirementSnapshotStatusProcessing":
      return (
        <>
          <Badge bg="warning" text="dark">
            Processing
          </Badge>
          <ProgressBar
            now={(status.completed / status.total) * 100}
            label={`${status.completed} / ${status.total}`}
            striped
            animated
            className="mt-1"
          />
        </>
      );

    case "RequirementSnapshotStatusDone":
      return <Badge bg="success">{status.status ?? "Done"}</Badge>;

    case "RequirementSnapshotStatusError":
      return <Badge bg="danger">Error</Badge>;

    case "RequirementSnapshotStatusDeleting":
      return <Badge bg="secondary">{status.status ?? "Deleting"}</Badge>;

    default:
      return <Badge bg="dark">Unknown Status</Badge>;
  }
};
