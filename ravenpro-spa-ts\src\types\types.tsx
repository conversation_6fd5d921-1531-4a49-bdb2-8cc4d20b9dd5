export type UserInfo = {
    email?: string;
    name?: string;
    [key: string]: unknown;
}

export type UserManualStatus = 'Deleting' | 'Done' | 'Error' | 'Processing' | 'Starting';

export type UserManualTag = {
    id: string;
    name: string;
};

export type UserManual = {
    id: string;
    author: string;
    createdAt: string; // ISO datetime string
    status: UserManualStatus;
    tags: UserManualTag[];
    progress?: number; // For processing status (0-100)
    fileName?: string;
    fileSize?: number;
};

export type SearchMode = 'mine' | 'tags';

export type SearchFilters = {
    mode: SearchMode;
    mineOnly?: boolean;
    statusFilter?: UserManualStatus[];
    tag?: string;
    firstDate?: string; // ISO date string
    lastDate?: string; // ISO date string
};

// Verification types
export type VerificationStatus = 'Deleting' | 'Done' | 'Error' | 'Running';
export type ProcessingStatus = 'Done' | 'Error' | 'Pending' | 'Processing';

export type VerificationTag = {
    id: string;
    name: string;
};

export type Verification = {
    id: string;
    createdAt: string; // ISO datetime string
    createdBy: string;
    documentId: string;
    documentVersion: string;
    rankingStatus: ProcessingStatus;
    reportingStatus: ProcessingStatus;
    snapshotId: string;
    status: VerificationStatus;
    verifyingStatus: ProcessingStatus;
    tags: VerificationTag[];
    progress?: number; // For processing/running status (0-100)
};

export type VerificationSearchMode = 'mine' | 'tags';

export type VerificationSearchFilters = {
    mode: VerificationSearchMode;
    mineOnly?: boolean;
    statusFilter?: VerificationStatus[];
    tag?: string;
    firstDate?: string; // ISO date string
    lastDate?: string; // ISO date string
};

// Step wizard types
export type RequirementSnapshot = {
    id: string;
    name: string;
    status: 'DONE';
    createdAt: string;
    description?: string;
};

export type UserManualPreprocessing = {
    id: string;
    name: string;
    status: 'DONE';
    createdAt: string;
    fileName?: string;
};

export type APIKeyType = 'Shared' | 'Owned';

export type APIKey = {
    id: string;
    name: string;
    type: APIKeyType;
    description?: string;
    isActive: boolean;
};

export type VerificationWizardStep = 'snapshot' | 'manual' | 'apikey' | 'confirm';

export type VerificationWizardData = {
    selectedSnapshot?: RequirementSnapshot;
    selectedManual?: UserManualPreprocessing;
    selectedAPIKey?: APIKey;
};