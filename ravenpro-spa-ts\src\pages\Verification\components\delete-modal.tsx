

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal, Toast } from "react-bootstrap";
import { useDeleteDocumentMutation, type Document } from "../../../gql/graphql";
import { PiWarning } from "react-icons/pi";

interface DeleteModalProps {
    open: boolean;
    onClose: (state: boolean) => void;
    handleRefetch: () => void;
    document: Document
}

export default function DeleteModal({ open, onClose, handleRefetch, document }: DeleteModalProps) {

    const [showToast, setShowToast] = useState<boolean>(false);
    const [toastMessage, setToastMessage] = useState<string>("");
    const [deleteDocument, { loading }] = useDeleteDocumentMutation();

    const handleDeleteDocument = (async () => {
        try {
            await deleteDocument({
                variables: {
                    documentId: document.documentId!,
                    version: document.version!
                }
            });
            setToastMessage(`Document ${document.documentId} | ${document.version} deleted successfully!`);
            handleCloseModal();
            setShowToast(true);
            handleRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const handleCloseModal = (() => {
        onClose(false);
    });

    return (
        <>
            <Modal show={open} onHide={handleCloseModal} centered>
                <Modal.Header closeButton>
                    <div>
                        <h5 className="mb-0">Delete Document</h5>
                    </div>
                </Modal.Header>
                <Modal.Body>
                    <Alert variant="danger">
                        <b><PiWarning></PiWarning> Warning! </b>You are about to delete the following Document: <br />
                        <ul>
                            <li><b>ID: </b>{document?.documentId} | <b>Version: </b>{document?.version}<br /></li>
                        </ul>
                        This action is irreversible. Deleting this snapshot may result in:
                        <ul>
                            <li>Loss of access to associated services or applications</li>
                            <li>Service disruptions or downtime</li>
                            <li>Security audit logs being impacted</li>
                            <li>Dependency failures if this snapshot is in active use</li>
                        </ul>
                    </Alert>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="light" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button onClick={handleDeleteDocument} variant="danger" disabled={loading}>
                        {loading ? "Deleting.." : "Delete Document"}
                    </Button>
                </Modal.Footer>
            </Modal>
            <Toast
                onClose={() => setShowToast(false)}
                show={showToast}
                delay={5000}
                autohide
                style={{
                    position: 'fixed',
                    bottom: 20,
                    right: 20,
                    minWidth: '200px',
                }}
            >
                <Toast.Header>
                    <strong className="me-auto">Notification</strong>
                </Toast.Header>
                <Toast.Body>{toastMessage}</Toast.Body>
            </Toast>

        </>
    )

}




