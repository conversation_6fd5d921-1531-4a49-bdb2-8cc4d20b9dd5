import React from "react";
import { Badge, ProgressBar } from "react-bootstrap";
import type { VerificationStatus } from "../../../gql/graphql";

interface StatusPillProps {
  status: VerificationStatus;
}


export const StatusPill: React.FC<StatusPillProps> = ({ status }) => {
  switch (status.__typename) {

    case "VerificationStatusRunning":
      return <Badge bg="primary">{status.status ?? "Running"}</Badge>;

  case "VerificationStatusDeleting":
      return <Badge bg="danger">{status.status ?? "Deleting"}</Badge>;

    case "VerificationStatusDone":
      return <Badge bg="success">{status.status ?? "Done"}</Badge>;

    case "VerificationStatusError":
      return <Badge bg="danger">Error</Badge>;

    default:
      return <Badge bg="dark">Starting..</Badge>;
  }
};
