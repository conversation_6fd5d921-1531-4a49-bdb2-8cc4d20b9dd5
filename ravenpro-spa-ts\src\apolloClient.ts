// import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
// import config from './config';

// const httpLink = createHttpLink({
//   uri: config.apollo.endpoint,
// });

// const client = new ApolloClient({
//   link: httpLink,
//   cache: new InMemoryCache(),
// });

// export default client;

import { OktaAuth } from '@okta/okta-auth-js';
import { ApolloClient, InMemoryCache, HttpLink, ApolloLink } from '@apollo/client';
import config from './config';

const oktaAuth = new OktaAuth(config.oidc);

// Middleware to add Authorization header
const authLink = new ApolloLink((operation, forward) => {
  return oktaAuth.tokenManager.getTokens().then(tokens => {
    const token = tokens.accessToken?.accessToken;

    operation.setContext({
      headers: {
        Authorization: token ? `Bearer ${token}` : '',
      },
    });

    return forward(operation);
  });
});

const httpLink = new HttpLink({ uri: config.apollo.endpoint });

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache(),
});

export default client;
