{"name": "ravenpro-spa-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000 --mode development", "start": "vite --port 3000", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@apollo/client": "^3.13.8", "@fontsource/inter": "^5.2.6", "@graphql-typed-document-node/core": "^3.2.0", "@okta/okta-auth-js": "^7.12.1", "@okta/okta-react": "^6.10.0", "@types/react-router-dom": "^5.3.3", "bootstrap": "^5.3.7", "dotenv": "^16.5.0", "graphql": "^16.11.0", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-select": "^5.10.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typescript-react-apollo": "^4.3.3", "@types/dotenv": "^6.1.1", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-select": "^5.0.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}