import React from "react";
import { Badge, ProgressBar } from "react-bootstrap";
import type { DocumentStatus } from "../../../gql/graphql";

interface StatusPillProps {
  status: DocumentStatus;
}

export const StatusPill: React.FC<StatusPillProps> = ({ status }) => {
  switch (status.__typename) {
    case "DocumentStatusStarted":
      return <Badge bg="primary">{"Pending Upload"}</Badge>;

    case "DocumentStatusUploaded":
      return <Badge bg="primary">{status.status ?? "Uploaded"}</Badge>;

  case "DocumentStatusSplitting":
      return <Badge bg="primary">{status.status ?? "Splitting"}</Badge>;

    case "DocumentStatusEmbedding":
      return (
        <>
          <Badge bg="primary">Embedding..</Badge>
          <ProgressBar
            now={(status.current / status.total) * 100}
            label={`${status.current} / ${status.total}`}
            striped
            animated
            className="mt-1"
          />
        </>
      );

    case "DocumentStatusAnnotating":
      return (
        <>
          <Badge bg="primary">Annotating..</Badge>
          <ProgressBar
            now={(status.current / status.total) * 100}
            label={`${status.current} / ${status.total}`}
            striped
            animated
            className="mt-1"
          />
        </>
      );

    case "DocumentStatusComplete":
      return <Badge bg="success">{status.status ?? "Done"}</Badge>;

    case "DocumentStatusError":
      return <Badge bg="danger">Error</Badge>;

    default:
      return <Badge bg="dark">Starting..</Badge>;
  }
};
