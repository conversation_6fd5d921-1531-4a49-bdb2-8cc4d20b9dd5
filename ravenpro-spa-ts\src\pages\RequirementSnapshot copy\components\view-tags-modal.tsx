import React, { useEffect, useState } from 'react';
import {
    <PERSON><PERSON>,
    Accordion,
    <PERSON><PERSON>,
    Badge,
    Form,
    Alert,
    Row,
    Col,
} from 'react-bootstrap';
import { useCreateRequirementTagMutation, useDeleteRequirementTagMutation, useGetRequirementTagsLazyQuery, useRenameRequirementTagMutation } from '../../../gql/graphql';

interface Tag {
    id: number;
    name: string;
}

interface ViewTagsModalProps {
    show: boolean;
    onHide: () => void;
    availableTags: Tag[];
    initialSelectedTags?: Tag[];
}

const ViewTagsModal: React.FC<ViewTagsModalProps> = ({
    show,
    onHide,
    availableTags,
    initialSelectedTags = [],
}) => {

    const [selectedTags, setSelectedTags] = useState<Tag[]>(initialSelectedTags);
    const [tagToAdd, setTagToAdd] = useState<number | undefined>();
    const [tagToManage, setTagToManage] = useState<number | undefined>();
    const [newTagName, setNewTagName] = useState('');
    const [renameTagName, setRenameTagName] = useState('');
    const [alert, setAlert] = useState<{ type: 'success' | 'danger'; message: string } | null>(null);

    const TOKENLIMIT = 100;

    // GraphQL
    const [fetchAvailTags, { loading: availTagsLoading, error: availTagsError, data: availTagsData }] = useGetRequirementTagsLazyQuery();
    const [deleteTag, { loading: deleteTagLoading, error: deleteTagError }] = useDeleteRequirementTagMutation();
    const [createTag, { loading: createTagLoading, error: createTagError }] = useCreateRequirementTagMutation();
    const [renameTag, { loading: renameTagLoading, error: renameTagError }] = useRenameRequirementTagMutation();

    useEffect(() => {
        fetchAvailTags({
            variables: {
                token: "",
                limit: TOKENLIMIT,
                prefix: ""
            }
        }).then((data) => {
            console.log(data);
        })
    }, ([]))

    const handleAddTag = () => {
        const tag = availableTags.find(t => t.id === tagToAdd);
        if (tag && !selectedTags.find(t => t.id === tag.id)) {
            setSelectedTags(prev => [...prev, tag]);
            setAlert({ type: 'success', message: `Tag "${tag.name}" added.` });
        } else {
            setAlert({ type: 'danger', message: 'Tag already added or invalid selection.' });
        }
    };

    const handleRemoveTag = (id: number) => {
        setSelectedTags(prev => prev.filter(t => t.id !== id));
        const removed = availableTags.find(t => t.id === id);
        setAlert({ type: 'success', message: `Tag "${removed?.name}" removed.` });
    };

    const handleCreateTag = () => {
        if (!newTagName.trim()) {
            setAlert({ type: 'danger', message: 'Tag name cannot be empty.' });
            return;
        }
        const exists = availableTags.some(t => t.name === newTagName);
        if (exists) {
            setAlert({ type: 'danger', message: 'Tag already exists.' });
            return;
        }
        // In real case: make API call to create new tag
        const newTag = { id: Date.now(), name: newTagName };
        availableTags.push(newTag);
        setAlert({ type: 'success', message: `Tag "${newTagName}" created.` });
        setNewTagName('');
    };

    const handleRenameTag = () => {
        if (!renameTagName.trim() || tagToManage === undefined) {
            setAlert({ type: 'danger', message: 'Invalid input for renaming.' });
            return;
        }
        const tag = availableTags.find(t => t.id === tagToManage);
        if (!tag) return;
        tag.name = renameTagName;
        setAlert({ type: 'success', message: `Tag renamed to "${renameTagName}".` });
        setRenameTagName('');
    };

    const handleDeleteTag = () => {
        if (tagToManage === undefined) {
            setAlert({ type: 'danger', message: 'No tag selected to delete.' });
            return;
        }
        const tag = availableTags.find(t => t.id === tagToManage);
        if (!tag) return;
        const index = availableTags.findIndex(t => t.id === tagToManage);
        availableTags.splice(index, 1);
        setSelectedTags(prev => prev.filter(t => t.id !== tagToManage));
        setAlert({ type: 'success', message: `Tag "${tag.name}" deleted.` });
        setTagToManage(undefined);
    };

    return (
        <Modal show={show} onHide={onHide} size="lg" centered>
            <Modal.Header closeButton>
                <Modal.Title>View Tags</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {alert && (
                    <Alert variant={alert.type} onClose={() => setAlert(null)} dismissible>
                        {alert.message}
                    </Alert>
                )}
                <Accordion defaultActiveKey="0">
                    {/* Tag Snapshot Accordion */}
                    <Accordion.Item eventKey="0">
                        <Accordion.Header>Tag Snapshot</Accordion.Header>
                        <Accordion.Body>
                            <div className="mb-3">
                                {selectedTags.map(tag => (
                                    <Badge key={tag.id} bg="secondary" className="me-2">
                                        {tag.name}{' '}
                                        <span
                                            role="button"
                                            onClick={() => handleRemoveTag(tag.id)}
                                            style={{ cursor: 'pointer', marginLeft: 4 }}
                                        >
                                            ×
                                        </span>
                                    </Badge>
                                ))}
                            </div>
                            <Form.Group as={Row} className="mb-3">
                                <Form.Label column sm={2}>Select Tag</Form.Label>
                                <Col sm={6}>
                                    <Form.Select
                                        value={tagToAdd ?? ''}
                                        onChange={(e) => setTagToAdd(Number(e.target.value))}
                                    >
                                        <option value="">Choose...</option>
                                        {availableTags.map(tag => (
                                            <option key={tag.id} value={tag.id}>
                                                {tag.name}
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Col>
                                <Col sm={4}>
                                    <Button variant="outline-dark" onClick={handleAddTag}>Add Tag</Button>
                                </Col>
                            </Form.Group>
                        </Accordion.Body>
                    </Accordion.Item>

                    {/* Manage Tags Accordion */}
                    <Accordion.Item eventKey="1">
                        <Accordion.Header>Manage Tags</Accordion.Header>
                        <Accordion.Body>
                            <Form.Group className="mb-3">
                                <Form.Label>New Tag Name</Form.Label>
                                <Form.Control
                                    type="text"
                                    value={newTagName}
                                    onChange={(e) => setNewTagName(e.target.value)}
                                    placeholder="Enter new tag"
                                />
                                <Button variant="outline-dark" className="mt-2" onClick={handleCreateTag}>
                                    Create Tag
                                </Button>
                            </Form.Group>
                            <Form.Group className="mb-3">
                                <Form.Label>Select Tag to Manage</Form.Label>
                                <Form.Select
                                    value={tagToManage ?? ''}
                                    onChange={(e) => setTagToManage(Number(e.target.value))}
                                >
                                    <option value="">Choose...</option>
                                    {availableTags.map(tag => (
                                        <option key={tag.id} value={tag.id}>
                                            {tag.name}
                                        </option>
                                    ))}
                                </Form.Select>
                            </Form.Group>
                            <Form.Group className="mb-3">
                                <Form.Label>Rename Tag</Form.Label>
                                <Form.Control
                                    type="text"
                                    value={renameTagName}
                                    onChange={(e) => setRenameTagName(e.target.value)}
                                    placeholder="Enter new name"
                                />
                                <Button variant="outline-dark" className="mt-2 me-2" onClick={handleRenameTag}>
                                    Rename
                                </Button>
                                <Button variant="danger" className="mt-2" onClick={handleDeleteTag}>
                                    Delete Tag
                                </Button>
                            </Form.Group>
                        </Accordion.Body>
                    </Accordion.Item>
                </Accordion>
            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={onHide}>
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

export default ViewTagsModal;