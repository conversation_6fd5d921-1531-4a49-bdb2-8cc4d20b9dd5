import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Spinner, ButtonGroup, Row, Form, Col } from "react-bootstrap";
import { <PERSON>C<PERSON><PERSON>, PiTag } from "react-icons/pi";
import {
    RequirementSnapshotStatusFilter,
    useGetRequirementSnapshotsLazyQuery,
    useGetRequirementTagsLazyQuery,
    useGetSnapshotsWithTagLazyQuery,
    useCreateRequirementTagMutation,
    useRenameRequirementTagMutation,
    useDeleteRequirementTagMutation
} from "../../gql/graphql";
import type { RequirementSnapshot } from "../../gql/graphql";
import { useEffect, useRef, useState } from "react";
import { StatusPill } from "./components/status-pill";
import CreateModal from "./components/create-modal";
import DeleteModal from "./components/delete-modal";
import ViewTagsModal from "./components/view-tags-modal";
import SnapshotSearchForm from "./components/snapshot-search-form";
import TagManager from "../../components/TagManager";
import Select from 'react-select';


function RequirementSnapshotPage() {

    const OWNER = "<EMAIL>";
    const TOKENLIMIT = 20;
    const TOKENLIMITMAX = 100;

    const lastISO = new Date().toISOString();
    const firstISO = new Date(new Date().setMonth(new Date().getMonth() - 6)).toISOString();

    const hasLoadedRef = useRef(false);
    const [snapshotsData, setSnapshotsData] = useState<RequirementSnapshot[]>([]);
    const [nextKey, setNextKey] = useState<string | null>(null);
    const [selectedEntry, setSelectedEntry] = useState<RequirementSnapshot>();

    const [mineOnly, setMineOnly] = useState<boolean>(false);
    const [status, setStatus] = useState<RequirementSnapshotStatusFilter>(RequirementSnapshotStatusFilter.All)
    const [first, setFirst] = useState<string>(firstISO);
    const [last, setLast] = useState<string>(lastISO);
    const [tags, setTags] = useState<string[]>([]);
    const [filterTags, setFilterTags] = useState<boolean>(false);
    const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
    const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
    const [showViewTagsModal, setShowViewTagsModal] = useState<boolean>(false);

    // Tag Manager State
    const [showTagManager, setShowTagManager] = useState<boolean>(false);

    // GraphQL Apollo Functions
    const [fetchSnapshots, { loading: snapshotsLoading, error: snapshotsError, data: snapshots }] = useGetRequirementSnapshotsLazyQuery();

    const [fetchAvailTags, { loading: availTagsLoading, error: availTagsError, data: availTagsData }] = useGetRequirementTagsLazyQuery();

    const [fetchSnapshotsTag, { loading: snapshotsTagLoading, error: snapshotsTagError, data: snapshotsTagData }] = useGetSnapshotsWithTagLazyQuery();

    // Tag Management Mutations
    const [createTag, { loading: createTagLoading, error: createTagError }] = useCreateRequirementTagMutation();
    const [renameTag, { loading: renameTagLoading, error: renameTagError }] = useRenameRequirementTagMutation();
    const [deleteTag, { loading: deleteTagLoading, error: deleteTagError }] = useDeleteRequirementTagMutation();

    const loadSnapshots = ((token: | string | null = null) => {
        fetchSnapshots({
            variables: {
                token,
                limit: TOKENLIMIT,
                mineOnly: mineOnly,
                statusFilter: status,
                first: first,
                last: last
            },
            fetchPolicy: 'network-only'
        })
    });

    const loadAvailTags =((token: | string | null = null) => {
        fetchAvailTags(
                {
                    variables: {
                        token: token,
                        limit: TOKENLIMITMAX,
                        prefix: ""
                    },
                    fetchPolicy: 'network-only'
                }
            )
    });

    const loadSnapshotsTag =((token: | string | null = null) => {
        fetchSnapshotsTag(
                {
                    variables: {
                        token: token,
                        limit: TOKENLIMIT,
                        first: first,
                        last: last,
                        tag: tags.length > 0 ? tags[0] : "" 
                    },
                    fetchPolicy: 'network-only'
                }
            )
    });

    const handleSnapshotsRefetch = (() => {
        setNextKey(null);
        setSnapshotsData([]);
        loadAvailTags();
        if (filterTags) {
            loadSnapshotsTag();
        } else {
            loadSnapshots();
        }
    });

    const handleTagChange = (selected: any) => {
        const selectedIds = selected ? selected.map((option: any) => option.value) : [];
        setTags(selectedIds);
    };

    // Tag Management Handlers for TagManager component
    const handleCreateTagManager = async (text: string) => {
        await createTag({
            variables: { text }
        });
        loadAvailTags(); // Refresh tags list
    };

    const handleUpdateTagManager = async (tag: any, newText: string) => {
        await renameTag({
            variables: {
                tagId: tag.id,
                oldText: tag.text,
                newText
            }
        });
        loadAvailTags(); // Refresh tags list
    };

    const handleDeleteTagManager = async (tag: any) => {
        await deleteTag({
            variables: {
                id: tag.id,
                text: tag.text
            }
        });
        loadAvailTags(); // Refresh tags list
    };

    const tagOptions = availTagsData?.requirementTags?.items?.map(item => ({
        value: item?.id,
        label: item?.text,
    })) ?? [];

    const selectedTagOptions = tagOptions.filter(option => tags.includes(option?.value!));

    const handleSearch = () => {
        handleSnapshotsRefetch();
    }

    function toReadableLocalTime(isoString: string): string {
        const date = new Date(isoString);

        // Format date as DD/MM/YYYY
        const datePart = date.toLocaleDateString(undefined, {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
        });

        // Format time as hh:mm am/pm
        const timePart = date.toLocaleTimeString(undefined, {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
        }).toLowerCase();

        return `${datePart} ${timePart}`;
    }

    const handleDelete = ((snapshot: RequirementSnapshot) => {
        setSelectedEntry(snapshot);
        setShowDeleteModal(true);
    });

    const handleViewTags = (() => {
        setShowViewTagsModal(true);
    });

    // Initial Load
    useEffect(() => {
        if (!hasLoadedRef.current) {
            hasLoadedRef.current = true;
            loadSnapshots();
            loadAvailTags();
        }
    }, []);

    // Dynamic update of data
    useEffect(() => {
        if (snapshots?.requirementSnapshots?.items) {
            const newItems = snapshots.requirementSnapshots.items ?? [];
            setSnapshotsData((prev) => [...prev, ...newItems]);
            setNextKey(snapshots.requirementSnapshots.nextToken ?? null);
        }
        if (snapshotsTagData?.snapshotsWithTag?.items) {
            const newItems = snapshotsTagData?.snapshotsWithTag?.items ?? [];
            setSnapshotsData((prev) => [...prev, ...newItems]);
            setNextKey(snapshotsTagData?.snapshotsWithTag?.nextToken ?? null);
        }
    }, [snapshots, snapshotsTagData])

    return (
        <>
            <Container className="mt-4">
                <Stack direction="horizontal" gap={2}>
                    <div className="p-2">
                        <h1><PiCamera></PiCamera> Requirement Snapshot</h1>
                        <p className="text-muted">Create and manage requirement snapshots from JAMA IDs.</p>
                    </div>
                    <div className="p-2 ms-auto d-flex gap-2">
                        <Button
                            variant="outline-primary"
                            onClick={() => setShowTagManager(true)}
                            className="d-flex align-items-center gap-2"
                        >
                            <PiTag /> Tag Manager
                        </Button>
                        <Button variant="dark" onClick={() => setShowCreateModal(true)}>+ Create New Requirement Snapshot</Button>
                    </div>
                </Stack>

                <Card>
                    <Card.Body>

                        <Form>
                            <Form.Check
                                type="switch"
                                id="custom-switch"
                                label={"Search Snapshots with Tag?"}
                                checked={filterTags}
                                onChange={() => setFilterTags(!filterTags)}
                            />
                        </Form>

                        <Row className="mb-2">
                            <Col md={3}>
                                <Form.Group controlId="firstDate">
                                    <Form.Label>Start Date</Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={first.slice(0, 10)}
                                        onChange={(e) => setFirst(new Date(e.target.value).toISOString())}
                                    />
                                </Form.Group>
                            </Col>
                            <Col md={3}>
                                <Form.Group controlId="lastDate">
                                    <Form.Label>End Date</Form.Label>
                                    <Form.Control
                                        type="date"
                                        value={last.slice(0, 10)}
                                        onChange={(e) => setLast(new Date(e.target.value).toISOString())}
                                    />
                                </Form.Group>
                            </Col>
                            {filterTags ? (
                                <>
                                    <Col md={6}>
                                        <Form.Group controlId="tags">
                                            <Form.Label>Search Tag</Form.Label>
                                            <Select
                                                isMulti
                                                options={tagOptions}
                                                value={selectedTagOptions}
                                                onChange={handleTagChange}
                                                placeholder="Select tags..."
                                            />
                                        </Form.Group>
                                    </Col>
                                </>
                            ) : (
                                <>
                                    <Col md={4}>
                                        <Form.Group controlId="statusFilter">
                                            <Form.Label>Status</Form.Label>
                                            <Form.Select
                                                value={status}
                                                onChange={(e) =>
                                                    setStatus(e.target.value as RequirementSnapshotStatusFilter)
                                                }
                                            >
                                                {Object.values(RequirementSnapshotStatusFilter).map((status) => (
                                                    <option key={status} value={status}>
                                                        {status}
                                                    </option>
                                                ))}
                                            </Form.Select>
                                        </Form.Group>
                                    </Col>
                                    <Col md={2}>
                                        <Form.Check
                                            type='checkbox'
                                            label="Mine Only"
                                            checked={mineOnly}
                                            onChange={(e) => setMineOnly(e.target.checked)}
                                        />
                                    </Col>
                                </>
                            )}


                        </Row>

                        <Row>
                            <Col md={2}>
                                <Button variant="outline-dark" onClick={handleSearch}>Search</Button>
                            </Col>
                        </Row>

                        {snapshotsError ? <Alert variant="danger">Failed to load snapshots</Alert> : (
                            <Table hover responsive>
                                <thead>
                                    <tr>
                                        <th colSpan={6}><h5>Available Requirement Snapshot(s)</h5></th>
                                    </tr>
                                    <tr>
                                        <th>ID</th>
                                        <th>Author</th>
                                        <th>Created</th>
                                        <th>Status</th>
                                        <th>Tags</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {snapshotsData.map((key) => (
                                        <tr key={key.id}>
                                            <td>{key.id}</td>
                                            <td>{key.author}</td>
                                            <td>{toReadableLocalTime(key.createdAt)}</td>
                                            <td>
                                                <StatusPill status={key.status!} />
                                            </td>
                                            <td>
                                                <Button variant="dark" size="sm" onClick={() => handleViewTags()}>View</Button>
                                            </td>
                                            <td>
                                                <Button variant="danger" size="sm" onClick={() => handleDelete(key)}>Delete</Button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </Table>
                        )}
                        <Button variant="dark" onClick={() => filterTags ? loadSnapshotsTag(nextKey) :loadSnapshots(nextKey)} disabled={snapshotsLoading || snapshotsTagLoading || nextKey == null}>{snapshotsLoading || snapshotsTagLoading? <Spinner animation='border'></Spinner> : nextKey == null ? "No More Data" :
                            "+ Load More"}</Button>
                    </Card.Body>
                </Card>

                <CreateModal open={showCreateModal} handleRefetch={handleSnapshotsRefetch} onClose={setShowCreateModal} />
                <DeleteModal open={showDeleteModal} handleRefetch={handleSnapshotsRefetch} onClose={setShowDeleteModal} snapshot={selectedEntry!} />
                <ViewTagsModal
                    show={showViewTagsModal}
                    onHide={() => setShowViewTagsModal(false)}
                    availableTags={[
                        { id: 1, name: 'HYDE PARK (F30)' }, //39acf9fb-d747-4076-a3e6-2c9970305e78
                        { id: 2, name: 'NEWCASTLE (F20)' }, // 6072c3b7-1d0d-4f30-93a9-ef5a8c9455ee
                    ]}
                    initialSelectedTags={[
                        { id: 1, name: 'HYDE PARK (F30)' },
                    ]}
                />

            </Container>

            {/* Tag Manager Component */}
            <TagManager
                show={showTagManager}
                onHide={() => setShowTagManager(false)}
                title="Requirement Tag Manager"
                tags={availTagsData?.requirementTags?.items?.filter(tag => tag !== null) || []}
                loading={availTagsLoading}
                error={availTagsError}
                onCreateTag={handleCreateTagManager}
                onUpdateTag={handleUpdateTagManager}
                onDeleteTag={handleDeleteTagManager}
                createLoading={createTagLoading}
                updateLoading={renameTagLoading}
                deleteLoading={deleteTagLoading}
                createError={createTagError}
                updateError={renameTagError}
                deleteError={deleteTagError}
            />
        </>
    )
}

export default RequirementSnapshotPage;