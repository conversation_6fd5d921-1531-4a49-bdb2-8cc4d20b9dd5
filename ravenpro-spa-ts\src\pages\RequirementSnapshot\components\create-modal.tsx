import { useEffect, useState, type ChangeEvent } from "react";
import { Button, Form, Modal, Toast } from "react-bootstrap";
import { useGetAccessibleKeysLazyQuery, useImportRequirementMutation } from "../../../gql/graphql";

interface CreateModalProps {
    open: boolean;
    onClose: (state: boolean) => void;
    handleRefetch: () => void;
}

export default function CreateModal({ open, onClose, handleRefetch }: CreateModalProps) {

    const [showToast, setShowToast] = useState<boolean>(false);
    const [toastMessage, setToastMessage] = useState<string>("");
    const [inputText, setInputText] = useState('');
    const [parsedList, setParsedList] = useState<string[]>([]);
    const [apiKey, setApiKey] = useState<string>("");

    const [createSnapshot, { loading }] = useImportRequirementMutation();
    const [fetchKeysData, { loading: keysLoading, error: keysError, data: keysData }] = useGetAccessibleKeysLazyQuery();

    const TOKENLIMIT = 1000;

    const handleCreate = (async () => {
        try {
            await createSnapshot({
                variables: {
                    apiKeyId: apiKey,
                    jamaIds: parsedList
                }
            });
            setToastMessage(`Requirements imported successfully!`);
            handleCloseModal();
            setShowToast(true);
            handleRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
        const value = e.target.value;
        setInputText(value);
        const list: string[] = value
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
        setParsedList(list);
    };

    const handleCloseModal = (() => {
        onClose(false);
        setInputText('');
        setApiKey('');
        setParsedList([]);
    });

    // Preload keys data
    useEffect(() => {
        fetchKeysData({
            variables: {
                limit: TOKENLIMIT,
                token: ""
            },
            fetchPolicy: 'network-only'
        })
    }, []);

    return (
        <>
            <Modal show={open} onHide={handleCloseModal} centered>
                <Modal.Header closeButton>
                    <div>
                        <h5 className="mb-0">Import JAMA IDs</h5>
                        <small className="text-muted">Key in the JAMA IDs separated by a new row for each document.</small>
                    </div>
                </Modal.Header>

                <Form>
                    <Modal.Body>
                        <Form.Group className="mb-2" controlId="multi">
                            <Form.Label>Select API Key: </Form.Label>
                            <Form.Select
                                value={apiKey}
                                onChange={(e) => setApiKey(e.target.value)}
                            >
                                {keysData ?
                                    <>
                                        <option key="" value="" selected></option>
                                        {keysData.accessibleKeys?.items?.map((key) => (
                                            <option key={key?.id} value={key?.id!}>{key?.name}</option>
                                        ))}
                                    </>
                                    :
                                    <>
                                        <option key="" disabled>Loading Keys..</option>
                                    </>
                                }
                            </Form.Select>
                            <Form.Label className="mt-2">Enter one ID per line:</Form.Label>
                            <Form.Control
                                as="textarea"
                                rows={5}
                                value={inputText}
                                onChange={handleChange}
                                placeholder="Enter one ID per line..."
                            />
                        </Form.Group>
                        <div style={{
                            maxHeight: '80px',
                            overflowY: 'auto',
                            border: '1px solid #dee2e6',
                            padding: '0.5rem',
                            borderRadius: '0.25rem',
                            backgroundColor: '#f8f9fa',
                        }}>
                        <Form.Text className="text-muted" style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                        JAMA IDs: {parsedList.join(', ')}
                        </Form.Text>
                        </div>
                        
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="light" onClick={handleCloseModal}>
                            Cancel
                        </Button>
                        <Button onClick={handleCreate} variant="dark" disabled={loading || parsedList.length === 0 || apiKey === ""}>
                            {loading ? "Importing Requirements.." : "Import Requirements"}
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal>
            <Toast
                onClose={() => setShowToast(false)}
                show={showToast}
                delay={5000}
                autohide
                style={{
                    position: 'fixed',
                    bottom: 20,
                    right: 20,
                    minWidth: '200px',
                }}
            >
                <Toast.Header>
                    <strong className="me-auto">Notification</strong>
                </Toast.Header>
                <Toast.Body>{toastMessage}</Toast.Body>
            </Toast>

        </>
    )

}
