mutation CreateApiKey($name: String!, $secret: String!) {
    createApi<PERSON>ey(name: $name, secret: $secret) {
        id
        name
        owner
        status
    }
}

mutation CreateRequirementTag($text: String) {
    createRequirementTag(text: $text) {
        createdAt
        id
        text
    }
}

mutation CreateVerification($documentId: String, $documentVersion: String, $snapshotId: String, $apiKeyId: String) {
    createVerification(documentId: $documentId, documentVersion: $documentVersion, snapshotId: $snapshotId, apiKeyId: $apiKeyId) {
        createdAt
        createdBy
        documentId
        documentVersion
        id
        rankingStatus {
            __typename
            ... on TaskStatusDone {
                status
            }
            ... on TaskStatusError {
                errorMessage
            }
            ... on TaskStatusPending {
                status
            }
            ... on TaskStatusProcessing {
                completed
                total
            }
        }
        reportingStatus {
            __typename
            ... on TaskStatusDone {
                status
            }
            ... on TaskStatusError {
                errorMessage
            }
            ... on TaskStatusPending {
                status
            }
            ... on TaskStatusProcessing {
                completed
                total
            }
        }
        snapshotId
        status {
            __typename
            ... on VerificationStatusDeleting {
                status
            }
            ... on VerificationStatusDone {
                status
            }
            ... on VerificationStatusError {
                status
            }
            ... on VerificationStatusRunning {
                status
            }
        }
        verifyingStatus {
            __typename
            ... on TaskStatusDone {
                status
            }
            ... on TaskStatusError {
                errorMessage
            }
            ... on TaskStatusPending {
                status
            }
            ... on TaskStatusProcessing {
                completed
                total
            }
        }
    }
}

mutation CreateVerificationTag($text: String) {
    createVerificationTag(text: $text) {
        createdAt
        id
        text
    }
}

mutation DeleteApiKey($sha: String!) {
    deleteApiKey(sha: $sha)
}

mutation DeleteRequirementSnapshot($snapshotId: String) {
    deleteRequirementSnapshot(snapshotId: $snapshotId)
}

mutation DeleteRequirementTag($id: String, $text: String) {
    deleteRequirementTag(id: $id, text: $text)
}

mutation DeleteVerification($verificationId: String) {
    deleteVerification(verificationId: $verificationId)
}

mutation DeleteVerificationTag($id: String, $text: String) {
    deleteVerificationTag(id: $id, text: $text)
}

mutation ImportRequirement($jamaIds: [String], $apiKeyId: String) {
    importRequirement(jamaIds: $jamaIds, apiKeyId: $apiKeyId) {
        author
        createdAt
        id
        status {
            __typename
            ... on RequirementSnapshotStatusStarting {
                status
            }
            ... on RequirementSnapshotStatusFetching {
                completed
                total
            }
            ... on RequirementSnapshotStatusProcessing {
                completed
                total
            }
            ... on RequirementSnapshotStatusDone {
                status
            }
            ... on RequirementSnapshotStatusError {
                errorMessage
            }
            ... on RequirementSnapshotStatusDeleting {
                status
            }
        }
        downloadUrl
    }
}

mutation RenameApiKey($sha: String!, $before: String!, $after: String!) {
    renameApiKey(sha: $sha, before: $before, after: $after) {
        id
        name
        owner
        status
    }
}

mutation RenameRequirementTag($tagId: String, $oldText: String, $newText: String) {
    renameRequirementTag(tagId: $tagId, oldText: $oldText, newText: $newText)
}

mutation RenameVerificationTag($tagId: String, $oldText: String, $newText: String) {
    renameVerificationTag(tagId: $tagId, oldText: $oldText, newText: $newText)
}

mutation RevokeApiKey($sha: String!, $email: String!) {
    revokeApiKey(sha: $sha, email: $email)
}

mutation ShareApiKey($sha: String!, $email: String!) {
    shareApiKey(sha: $sha, email: $email) {
        keyID
        accessorEmail
    }
}

mutation TransferApiKey($sha: String!, $email: String!) {
    transferApiKey(sha: $sha, email: $email)
}

mutation TagRequirementSnapshot($snapshotId: String, $tagId: String, $tagText: String) {
    tagRequirementSnapshot(snapshotId: $snapshotId, tagId: $tagId, tagText: $tagText)
}

mutation UntagRequirementSnapshot($snapshotId: String, $tagId: String) {
    untagRequirementSnapshot(snapshotId: $snapshotId, tagId: $tagId)
}

mutation TagVerification($verificationId: String, $tagId: String, $tagText: String) {
    tagVerification(verificationId: $verificationId, tagId: $tagId, tagText: $tagText)
}

mutation UntagVerification($verificationId: String, $tagId: String) {
    untagVerification(verificationId: $verificationId, tagId: $tagId)
}

mutation CreateDocument($documentId: String!, $version: String!, $apiKeyId: String) {
    createDocument(documentId: $documentId, version: $version, apiKeyId: $apiKeyId) {
        documentId
        endTime {
            value
        }
        startTime
        status {
            __typename
            ... on DocumentStatusStarted {
                status
            }
            ... on DocumentStatusUploaded {
                status
            }
            ... on DocumentStatusSplitting {
                status
            }
            ... on DocumentStatusAnnotating {
                current
                total
            }
            ... on DocumentStatusEmbedding {
                current
                total
            }
            ... on DocumentStatusComplete {
                status
            }
            ... on DocumentStatusError {
                errorMessage
            }
        }
        updatedBy
        version
        uploadUrl {
            value
        }
    }
}

mutation DeleteDocument($documentId: String!, $version: String!) {
    deleteDocument(documentId: $documentId, version: $version)
}
