import React, { useState } from 'react';
import {
  Form,
  Button,
  Row,
  Col,
} from 'react-bootstrap';
import Select from 'react-select';
import { RequirementSnapshotStatusFilter } from '../../../gql/graphql';


interface SnapshotFilter {
    mineOnly?: boolean,
    statusFilter?: RequirementSnapshotStatusFilter,
    first: string,
    last: string,
    tags?: string[]
}

interface SearchProps {
  initialFilter: SnapshotFilter;
  availableTags: string[];
  onSearch: (filter: SnapshotFilter) => void;
}

const SnapshotSearchForm: React.FC<SearchProps> = ({
  initialFilter,
  availableTags,
  onSearch,
}) => {
  const [mineOnly, setMineOnly] = useState(initialFilter.mineOnly);
  const [statusFilter, setStatusFilter] = useState(initialFilter.statusFilter);
  const [first, setFirst] = useState(initialFilter.first);
  const [last, setLast] = useState(initialFilter.last);
  const [tags, setTags] = useState<string[]>(initialFilter.tags || []);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const tagOptions = availableTags.map(tag => ({
    label: tag,
    value: tag
  }));

  const handleTagChange = (selected: any) => {
    setTags(selected ? selected.map((option: any) => option.value) : []);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch({
      mineOnly,
      statusFilter,
      first: new Date(first).toISOString(),
      last: new Date(last).toISOString(),
      tags,
    });
  };

  return (
    <Form onSubmit={handleSubmit} className="p-3 border rounded bg-light">
      <Row className="mb-3">
        <Col md={2}>
          <Form.Check
            type="checkbox"
            label="Mine Only"
            checked={mineOnly}
            onChange={(e) => setMineOnly(e.target.checked)}
          />
        </Col>
        <Col md={3}>
          <Form.Group controlId="statusFilter">
            <Form.Label>Status</Form.Label>
            <Form.Select
              value={statusFilter}
              onChange={(e) =>
                setStatusFilter(e.target.value as RequirementSnapshotStatusFilter)
              }
            >
              {Object.values(RequirementSnapshotStatusFilter).map((status) => (
                <option key={status} value={status}>
                  {status}
                </option>
              ))}
            </Form.Select>
          </Form.Group>
        </Col>
        <Col md={3}>
          <Form.Group controlId="firstDate">
            <Form.Label>Start Date</Form.Label>
            <Form.Control
              type="date"
              value={first.slice(0, 10)}
              onChange={(e) => setFirst(new Date(e.target.value).toISOString())}
            />
          </Form.Group>
        </Col>
        <Col md={3}>
          <Form.Group controlId="lastDate">
            <Form.Label>End Date</Form.Label>
            <Form.Control
              type="date"
              value={last.slice(0, 10)}
              onChange={(e) => setLast(new Date(e.target.value).toISOString())}
            />
          </Form.Group>
        </Col>
        <Col md={1} className="d-flex align-items-end">
          <Button
            variant="outline-secondary"
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            {showAdvanced ? 'Basic' : 'Advanced'}
          </Button>
        </Col>
      </Row>

      {showAdvanced && (
        <Row className="mb-3">
          <Col md={6}>
            <Form.Group controlId="tags">
              <Form.Label>Tags</Form.Label>
              <Select
                isMulti
                options={tagOptions}
                value={tagOptions.filter(opt => tags.includes(opt.value))}
                onChange={handleTagChange}
                placeholder="Select tags..."
              />
            </Form.Group>
          </Col>
        </Row>
      )}

      <Row>
        <Col className="d-flex justify-content-end">
          <Button type="submit" variant="primary">
            Search
          </Button>
        </Col>
      </Row>
    </Form>
  );
};

//  <SnapshotSearchForm
//                 initialFilter={{
//                     mineOnly: true,
//                     statusFilter: RequirementSnapshotStatusFilter.All,
//                     first: new Date().toISOString(),
//                     last: new Date().toISOString(),
//                     tags: ['urgent', 'ui']
//                 }}
//                 availableTags={['urgent', 'ui', 'backend', 'qa']}
//                 onSearch={(filter) => {
//                     console.log('Searching with filter:', filter);
//                     setFilter(filter); // your own state
//                 }}
//             />
export default SnapshotSearchForm;
