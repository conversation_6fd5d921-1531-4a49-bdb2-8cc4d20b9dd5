import { Security } from '@okta/okta-react';
import { OktaAuth, toRelativeUrl } from '@okta/okta-auth-js';
import { useNavigate } from 'react-router-dom';
import config from './config';
import AppRoutes from './components/AppRoutes';
import NavBar from './components/NavBar';
import './App.css'
import 'bootstrap/dist/css/bootstrap.min.css';

const oktaAuth = new OktaAuth(config.oidc);

function App() {

  const navigate = useNavigate();
  const restoreOriginalUri = (_oktaAuth: OktaAuth,  originalUri: string) => {
    navigate(toRelativeUrl(originalUri || '/', window.location.origin));
  };

  return (
    <Security oktaAuth={oktaAuth} restoreOriginalUri={restoreOriginalUri}>
      <header>
        <NavBar/>
      </header>
      <main>
        <AppRoutes />
      </main>
    </Security>
  );
}

export default App;
