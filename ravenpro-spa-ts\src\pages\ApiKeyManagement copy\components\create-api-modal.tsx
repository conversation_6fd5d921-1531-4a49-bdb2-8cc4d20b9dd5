import { useState } from "react";
import { But<PERSON>, <PERSON>, Modal, Toast } from "react-bootstrap";
import { useCreateApiKeyMutation } from "../../../gql/graphql";

interface CreateApiKeyModalProps {
    open: boolean;
    onClose: (state:boolean) => void;
    handleKeysRefetch: () => void;
}

export default function CreateApiKeyModal({ open, onClose, handleKeysRefetch }: CreateApiKeyModalProps) {

    const [keyName, setKeyName] = useState<string>("");
    const [keySecret, setKeySecret] = useState<string>("");
    const [showToast, setShowToast] = useState<boolean>(false);
    const [toastMessage, setToastMessage] = useState<string>("");

    const [createApiKey, { loading }] = useCreateApiKeyMutation();

    const handleCreateApiKey = ( async () => {
        try {
            await createApiKey({
                variables: {
                    name: keyName,
                    secret: keySecret
                }
            });
            setToastMessage(`ApiKey: ${keyName} created successfully!`);
            handleCloseModal();
            setShowToast(true);
            handleKeysRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const handleCloseModal = (() => {
        onClose(false);
        setKeyName("");
        setKeySecret("");        
    });

    return (
        <>
        <Modal show={open} onHide={handleCloseModal} centered>
            <Modal.Header closeButton>
                <div>
                    <h5 className="mb-0">Create API Key</h5>
                    <small className="text-muted">Create a new API key with a descriptive name and secure value.</small>
                </div>
            </Modal.Header>

            <Form>
                <Modal.Body>
                    <Form.Group className="mb-2">
                        <Form.Label><h6>API Key Name</h6></Form.Label>
                        <Form.Control
                            required
                            type="text"
                            onChange={(e) => setKeyName(e.target.value)}
                            placeholder="eg. VnV Dev Key, TAP Dev Key"
                        />
                    </Form.Group>

                    <Form.Group className="mb-2">
                        <Form.Label><h6>API Secret</h6></Form.Label>
                        <Form.Control
                            required
                            type="password"
                            onChange={(e) => setKeySecret(e.target.value)}
                            placeholder="Enter your API Secret"
                        />
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="light" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button onClick={handleCreateApiKey} variant="dark" disabled={loading || !keyName.trim() || !keySecret.trim()}>
                        {loading ? "Creating key.." : "Create API Key"}
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal>
        <Toast
            onClose={() => setShowToast(false)}
            show={showToast}
            delay={5000}
            autohide
            style={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            minWidth: '200px',
            }}
        >
            <Toast.Header>
            <strong className="me-auto">Notification</strong>
            </Toast.Header>
            <Toast.Body>{toastMessage}</Toast.Body>
        </Toast>

        </>
    )

}




