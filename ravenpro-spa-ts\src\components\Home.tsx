import { useOktaAuth } from '@okta/okta-react';
import { useState, useEffect } from 'react';
import { Con<PERSON>er, Button, Row, Col, Card } from 'react-bootstrap';
import Loading from '../components/Loading';
import type { UserInfo } from '../types/types';
import { PiKey, PiArrowRight, PiSealCheck, PiNotebook, PiCamera } from 'react-icons/pi';
import { Link } from 'react-router-dom';

const Home = () => {
    const { authState, oktaAuth } = useOktaAuth();
    const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
    const [owner, setOwner] = useState<string>("");

    useEffect(() => {
        if (!authState || !authState.isAuthenticated) {
            // When user isn't authenticated, forget any user info
            setUserInfo(null);
        } else {
            oktaAuth.getUser().then((info) => {
                setUserInfo(info);
            });
        }
    }, [authState, oktaAuth]); // Update if authState changes

    useEffect(() => {
        if (userInfo?.name) {
            setOwner(userInfo?.name!);
        }
    }, [userInfo])

    const login = async () => {
        await oktaAuth.signInWithRedirect();
    };

    return (
        <div>
            <div>
                {!authState || !authState.isAuthenticated || !userInfo
                    &&
                    <Loading />
                }

                <Container className="d-flex flex-column justify-content-center align-items-center" style={{ minHeight: '70vh' }}>
                    <Row className="w-100 justify-content-center">
                        {authState?.isAuthenticated && userInfo && (
                            <>
                                <Col className="text-center">
                                    <h1>Welcome to RavenPro Cloud!</h1>
                                    <p>Automated user manual verification services with seamless JAMA integration for capturing and reviewing requirements snapshots.</p>
                                </Col>
                                <Row>
                                    <Col>
                                        <Link to="/apikeymanagement" style={{ textDecoration: 'none', color: 'inherit' }}>
                                            <Card className="hover-shadow h-100" style={{ cursor: 'pointer' }}>
                                                <Card.Body>
                                                    <Card.Title><h3><PiKey></PiKey> API Key Management <PiArrowRight></PiArrowRight></h3></Card.Title>
                                                    <Card.Text>Manage your API keys, share them with team members, and control access.</Card.Text>
                                                </Card.Body>
                                            </Card>
                                        </Link>
                                    </Col>
                                    <Col>
                                        <Link to="/requirementsnapshot" style={{ textDecoration: 'none', color: 'inherit' }}>
                                            <Card className="hover-shadow h-100" style={{ cursor: 'pointer' }}>
                                                <Card.Body>
                                                    <Card.Title><h3><PiCamera></PiCamera> Requirement Snapshot <PiArrowRight></PiArrowRight></h3></Card.Title>
                                                    <Card.Text>Create and manage requirement snapshots from JAMA IDs.</Card.Text>
                                                </Card.Body>
                                            </Card>
                                        </Link>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col>
                                        <Link to="/usermanual" style={{ textDecoration: 'none', color: 'inherit' }}>
                                            <Card className="mt-4 hover-shadow h-100" style={{ cursor: 'pointer' }}>
                                                <Card.Body>
                                                    <Card.Title><h3><PiNotebook></PiNotebook> User Manual Preprocessing <PiArrowRight></PiArrowRight></h3></Card.Title>
                                                    <Card.Text>Upload your user manual to be preprocessed for verification.</Card.Text>
                                                </Card.Body>
                                            </Card>
                                        </Link>
                                    </Col>
                                    <Col>
                                        <Link to="/verification" style={{ textDecoration: 'none', color: 'inherit' }}>
                                            <Card className="mt-4 hover-shadow h-100" style={{ cursor: 'pointer' }}>
                                                <Card.Body>
                                                    <Card.Title><h3><PiSealCheck></PiSealCheck> Verification <PiArrowRight></PiArrowRight></h3></Card.Title>
                                                    <Card.Text>Use GenAI to verify your user manual against the JAMA requirements.</Card.Text>
                                                </Card.Body>
                                            </Card>
                                        </Link>
                                    </Col>
                                </Row>

                            </>



                        )}
                        {!authState?.isAuthenticated && (
                            <Col xs={12} sm={8} md={6} lg={4}>
                                <Card className="p-4 shadow-sm">
                                    <Card.Img variant="top" src="/src/assets/logo_word_large.png"></Card.Img>
                                    <hr></hr>
                                    <Card.Body>
                                        <h2 className="text-center mb-4">RavenPro Cloud</h2>
                                        <Button variant="dark" className="w-100" onClick={login}>
                                            Login via <b>OKTA</b>
                                        </Button>
                                    </Card.Body>
                                </Card>
                            </Col>
                        )}
                    </Row>
                </Container>
            </div>
        </div>
    );
};
export default Home;
