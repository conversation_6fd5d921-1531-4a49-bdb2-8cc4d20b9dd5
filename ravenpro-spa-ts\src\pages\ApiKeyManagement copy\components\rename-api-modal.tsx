

import { useState } from "react";
import { But<PERSON>, Form, Modal, Toast } from "react-bootstrap";
import { useRenameApiKeyMutation, type ApiK<PERSON> } from "../../../gql/graphql";

interface RenameApiKeyModalProps {
    open: boolean;
    onClose: (state: boolean) => void;
    handleKeysRefetch: () => void;
    apiKey: ApiKey
}

export default function RenameApiKeyModal({ open, onClose, handleKeysRefetch, apiKey }: RenameApiKeyModalProps) {

    const [showToast, setShowToast] = useState<boolean>(false);
    const [toastMessage, setToastMessage] = useState<string>("");
    const [newName, setNewName] = useState<string>("");
    const [renameApiKey, { loading }] = useRenameApiKeyMutation();

    const handleRenameApiKey = (async () => {
        try {
            await renameApiKey({
                variables: {
                    sha: apiKey.id!,
                    before: apiKey.name!,
                    after: newName
                }
            });
            setToastMessage(`ApiKey ${apiKey.name} renamed to ${newName} successfully!`);
            handleCloseModal();
            setShowToast(true);
            handleKeysRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const handleCloseModal = (() => {
        onClose(false);
    });

    return (
        <>
            <Modal show={open} onHide={handleCloseModal} centered>
                <Modal.Header closeButton>
                    <div>
                        <h5 className="mb-0">Rename API Key</h5>
                    </div>
                </Modal.Header>
                <Modal.Body>
                    <Form.Group className="mb-2">
                        <Form.Label><h6>Rename APIKey [{apiKey?.name}] to: </h6></Form.Label>
                        <Form.Control
                            required
                            type="text"
                            onChange={(e) => setNewName(e.target.value)}
                            placeholder="eg. Development API"
                        />
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="light" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button onClick={handleRenameApiKey} variant="dark" disabled={loading || !newName.trim()}>
                        {loading ? "Renaming key.." : "Rename API Key"}
                    </Button>
                </Modal.Footer>
            </Modal>
            <Toast
                onClose={() => setShowToast(false)}
                show={showToast}
                delay={5000}
                autohide
                style={{
                    position: 'fixed',
                    bottom: 20,
                    right: 20,
                    minWidth: '200px',
                }}
            >
                <Toast.Header>
                    <strong className="me-auto">Notification</strong>
                </Toast.Header>
                <Toast.Body>{toastMessage}</Toast.Body>
            </Toast>

        </>
    )

}




