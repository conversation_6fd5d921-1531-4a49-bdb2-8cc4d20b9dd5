import { useState, useEffect } from 'react';
import { useOktaAuth } from '@okta/okta-react';
import { NavLink } from 'react-router-dom';
import { Container, Nav, Navbar, Badge } from 'react-bootstrap';
import { FaChalkboardUser } from "react-icons/fa6";
import { PiKey, PiCamera, PiNotebook, PiSealCheck } from "react-icons/pi";
import type { UserInfo } from "../types/types";

const NavBar = () => {

  const { authState, oktaAuth } = useOktaAuth();
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!authState || !authState.isAuthenticated) {
      setUserInfo(null);
    } else {
      setUserInfo(authState.idToken?.claims as UserInfo);
      // console.log(JSON.stringify(authState));
    }
  }, [authState, oktaAuth]); // Update if authState changes

  const logout = async () => {
    setLoading(true);
    try {
      await oktaAuth.signOut();
    } catch (err) {
      console.error('Logout failed:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!authState || !userInfo) {
    return null;
  }

  return (
    <>
      {authState.isAuthenticated && (
        <Navbar expand="lg" bg="dark" data-bs-theme="dark" sticky="top">
          <Container>
            <Navbar.Brand href="/">
              RavenPro Cloud <small><Badge pill bg="warning" text="dark">Dev</Badge></small> |
            </Navbar.Brand>
            <Navbar.Toggle aria-controls="navbar-nav" />
            <Navbar.Collapse id="navbar-nav">
              <Nav className="me-auto">
                <Nav.Link as={NavLink} to="/apikeymanagement">
                  <PiKey></PiKey> APIKeys
                </Nav.Link>
                <Nav.Link as={NavLink} to="/requirementsnapshot">
                  <PiCamera></PiCamera> Requirement
                </Nav.Link>
                <Nav.Link as={NavLink} to="/usermanual">
                  <PiNotebook></PiNotebook> UserManual
                </Nav.Link>
                <Nav.Link as={NavLink} to="/verification">
                  <PiSealCheck></PiSealCheck> Verification
                </Nav.Link>
              </Nav>
              <Nav className="ms-auto">
                <Nav.Link className="no-hover" disabled>
                  <FaChalkboardUser />
                  {' '} | {userInfo?.name}
                  
                </Nav.Link>
                <Nav.Link onClick={logout} disabled={loading}>
                  {loading ? (
                    "Logging Out..."
                  ) : (
                    'Logout'
                  )}
                </Nav.Link>
              </Nav>
            </Navbar.Collapse>
          </Container>
        </Navbar>
      )}
    </>
  );

};
export default NavBar;
