

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Button, Form, Modal, Table, Toast } from "react-bootstrap";
import { type ApiKeyAccessRight, useGetAccessorsLazyQuery, useShareApiKeyMutation, type ApiKey, useRevokeApiKeyMutation } from "../../../gql/graphql";
import { PiWarning } from "react-icons/pi";

interface ShareApiKeyModalProps {
    open: boolean;
    onClose: (state: boolean) => void;
    handleKeysRefetch: () => void;
    apiKey: ApiKey
}

export default function ShareApiKeyModal({ open, onClose, handleKeysRefetch, apiKey }: ShareApiKeyModalProps) {

    const OWNER = "<EMAIL>";
    const TOKENLIMIT = 100;

    const [showToast, setShowToast] = useState<boolean>(false);
    const [toastMessage, setToastMessage] = useState<string>("");
    const [email, setEmail] = useState<string>("");
    const [shareApiKey, { loading: shareLoading }] = useShareApiKeyMutation();

    const [accessorsData, setAccessorsData] = useState<ApiKeyAccessRight[]>([]);
    const [nextKey, setNextKey] = useState<string | null>(null);

    const [fetchAccessors, { loading: accessorLoading, data: accessors, error: accessorsError }] = useGetAccessorsLazyQuery();
    const [revokeAccessor, { loading: revokeLoading }] = useRevokeApiKeyMutation();

    const loadAccessors = ((token: string | null = null) => {
        if (apiKey) {
            fetchAccessors({
                variables: {
                    token,
                    limit: TOKENLIMIT,
                    sha: apiKey.id!
                },
                fetchPolicy: 'network-only'
            })
        }
    });

    const handleAccessorsRefetch = (() => {
        setNextKey(null);
        setAccessorsData([]);
        loadAccessors();
    });

    useEffect(() => {
        if (apiKey) {
            setAccessorsData([]);
            setNextKey(null);
            loadAccessors();
        }
    }, [apiKey]);

    useEffect(() => {
        if (accessors?.accessors?.items && apiKey) {
            const newItems = accessors.accessors.items ?? [];
            const filteredItems = newItems.filter((item): item is ApiKeyAccessRight => item !== null);
            setAccessorsData((prev) => [...prev, ...filteredItems]);
            setNextKey(accessors.accessors.nextToken ?? null);
        }
    }, [accessors]);

    const handleRevoke = (async (accessorEmail: string) => {
        try {
            await revokeAccessor({
                variables: {
                    sha: apiKey.id!,
                    email: accessorEmail
                }
            });
            setToastMessage(`ApiKey ${apiKey.name} revoked on ${accessorEmail} successfully!`);
            handleCloseModal();
            setShowToast(true);
            handleAccessorsRefetch();
            handleKeysRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const handleShareApiKey = (async () => {
        try {
            await shareApiKey({
                variables: {
                    sha: apiKey.id!,
                    email: email
                }
            });
            setToastMessage(`ApiKey ${apiKey.name} shared to ${email} successfully!`);
            handleCloseModal();
            setShowToast(true);
            handleAccessorsRefetch();
            handleKeysRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const handleCloseModal = (() => {
        onClose(false);
    });

    return (
        <>
            <Modal show={open} onHide={handleCloseModal} centered>
                <Modal.Header closeButton>
                    <div>
                        <h5 className="mb-0">Share API Key</h5>
                    </div>
                </Modal.Header>
                <Modal.Body>
                    <Alert variant="warning">
                        <b><PiWarning></PiWarning> Warning! </b>Please ensure the email is correct before sharing.
                    </Alert>
                    {accessorsError ? <Alert variant="danger">Failed to load accessors</Alert> : (
                        <Table hover responsive>
                            <tbody>
                                {accessorsData.filter(key => key.accessorEmail !== OWNER).map((key) => (
                                    <tr key={key.accessorEmail}>
                                        <td>{key.accessorEmail}</td>
                                        <td><Button size="sm" variant="outline-danger" onClick={() => handleRevoke(key.accessorEmail!)}>Revoke</Button></td>
                                    </tr>
                                ))}
                            </tbody>
                        </Table>
                    )}
                    {/* <Button variant="dark" onClick={() => loadAccessors(nextKey)} disabled={accessorLoading || nextKey == null}>{accessorLoading ? <Spinner animation='border'></Spinner> : nextKey == null ? "No More Data" :
                                "+ Load More"}</Button> */}

                    <Form.Group className="mb-2">
                        <Form.Label><h6>Share APIKey [{apiKey?.name}] with: </h6></Form.Label>
                        <Form.Control
                            required
                            type="email"
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder="<EMAIL>"
                        />
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="light" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button onClick={handleShareApiKey} variant="dark" disabled={shareLoading || revokeLoading || !email.trim()}>
                        {shareLoading || revokeLoading ? "Loading.." : "Share API Key"}
                    </Button>
                </Modal.Footer>
            </Modal>
            <Toast
                onClose={() => setShowToast(false)}
                show={showToast}
                delay={5000}
                autohide
                style={{
                    position: 'fixed',
                    bottom: 20,
                    right: 20,
                    minWidth: '200px',
                }}
            >
                <Toast.Header>
                    <strong className="me-auto">Notification</strong>
                </Toast.Header>
                <Toast.Body>{toastMessage}</Toast.Body>
            </Toast>

        </>
    )

}




