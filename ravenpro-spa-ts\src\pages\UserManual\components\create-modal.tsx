import { useEffect, useState, type ChangeEvent } from "react";
import { Button, Form, InputGroup, Modal, Toast } from "react-bootstrap";
import { useCreateDocumentMutation, useGetAccessibleKeysLazyQuery, useImportRequirementMutation } from "../../../gql/graphql";

interface CreateModalProps {
    open: boolean;
    onClose: (state: boolean) => void;
    handleRefetch: () => void;
}

export default function CreateModal({ open, onClose, handleRefetch }: CreateModalProps) {

    const [showToast, setShowToast] = useState<boolean>(false);
    const [toastMessage, setToastMessage] = useState<string>("");
    const [apiKey, setApiKey] = useState<string>("");
    const [file, setFile] = useState<File | null>(null);
    const [documentId, setDocumentId] = useState<string>("");
    const [version, setVersion] = useState<string>("");
    const [created, setCreated] = useState<boolean>(false);

    const [createDocument, { loading, error, data }] = useCreateDocumentMutation();
    const [fetchKeysData, { loading: keysLoading, error: keysError, data: keysData }] = useGetAccessibleKeysLazyQuery();

    const TOKENLIMIT = 1000;

    const handleCreate = (async () => {
        try {
            await createDocument({
                variables: {
                    documentId: documentId,
                    apiKeyId: apiKey,
                    version: version
                }
            });
            setCreated(true);

            // setToastMessage(`Document uploaded successfully!`);
            // handleCloseModal();
            // setShowToast(true);
            // handleRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const uploadToS3 = async (file: File, presignedUrl: string): Promise<void> => {
        try {
            console.log(presignedUrl);
            const response = await fetch(presignedUrl, {
            method: 'PUT',
            headers: {
                'Content-Type': file.type, // S3 expects the content-type to match
            },
            body: file,
            });

            if (!response.ok) {
                console.log(`Upload failed with status ${response.status}`);
            throw new Error(`Upload failed with status ${response.status}`);
            }

            console.log('Upload successful!');
            handleCloseModal();
            setShowToast(true);
            handleRefetch();

            } catch (err) {
                handleCloseModal();
                setToastMessage('File upload error');
                setShowToast(true);
                handleRefetch();
                console.error('Upload error:', err);

            }
        };

    useEffect(()=>{
        if (data && created) {
            setToastMessage(`Document uploaded successfully!`);
            uploadToS3(file!, data.createDocument?.uploadUrl?.value!);
        }
    }, [created, data])


    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
        setFile(e.target.files[0]);
        }
    };

    const handleCloseModal = (() => {
        onClose(false);
        setFile(null);
        setCreated(false);
        setDocumentId("");
        setVersion("");
        setApiKey('');
    });


    const getSanitizedFileName = (fileName: string): string => {
    const lastDotIndex = fileName.lastIndexOf('.');
    const nameWithoutExtension =
        lastDotIndex === -1 ? fileName : fileName.substring(0, lastDotIndex);
    return nameWithoutExtension.replace(/\s+/g, '_');
    };

    // Preload keys data
    useEffect(() => {
        fetchKeysData({
            variables: {
                limit: TOKENLIMIT,
                token: ""
            },
            fetchPolicy: 'network-only'
        })
    }, []);

    useEffect(() => {
        if (file) {
            setDocumentId(getSanitizedFileName(file.name));
        }
    }, [file])

    return (
        <>
            <Modal show={open} onHide={handleCloseModal} centered>
                <Modal.Header closeButton>
                    <div>
                        <h5 className="mb-0">Import Document</h5>
                        <small className="text-muted">Upload document to be pre-processed for verification.</small>
                    </div>
                </Modal.Header>

                <Form>
                    <Modal.Body>
                        <Form.Group className="mb-2" controlId="multi">
                            <Form.Label>Select API Key: </Form.Label>
                            <Form.Select
                                value={apiKey}
                                onChange={(e) => setApiKey(e.target.value)}
                            >
                                {keysData ?
                                    <>
                                        <option key="" value="" selected></option>
                                        {keysData.accessibleKeys?.items?.map((key) => (
                                            <option key={key?.id} value={key?.id!}>{key?.name}</option>
                                        ))}
                                    </>
                                    :
                                    <>
                                        <option key="" disabled>Loading Keys..</option>
                                    </>
                                }
                            </Form.Select>
                            <Form.Label className="mt-2">Upload document</Form.Label>
                            <Form.Control
                            type="file"
                            onChange={handleChange}
                            >
                            </Form.Control>


                        </Form.Group>
                        <InputGroup>
                                <InputGroup.Text>DocumentID</InputGroup.Text>
                                <Form.Control
                                type="text"
                                value={documentId}
                                onChange={(e)=>setDocumentId(e.target.value)}
                                ></Form.Control>
                                <InputGroup.Text>Version</InputGroup.Text>
                                <Form.Control
                                type="text"
                                value={version}
                                onChange={(e)=>setVersion(e.target.value)}
                                ></Form.Control>
                        </InputGroup>
                        
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="light" onClick={handleCloseModal}>
                            Cancel
                        </Button>
                        <Button onClick={handleCreate} variant="dark" disabled={loading || apiKey === "" || file === null || version === "" || documentId === ""}>
                            {loading ? "Uploading documents.." : "Upload Document"}
                        </Button>
                    </Modal.Footer>
                </Form>
            </Modal>
            <Toast
                onClose={() => setShowToast(false)}
                show={showToast}
                delay={5000}
                autohide
                style={{
                    position: 'fixed',
                    bottom: 20,
                    right: 20,
                    minWidth: '200px',
                }}
            >
                <Toast.Header>
                    <strong className="me-auto">Notification</strong>
                </Toast.Header>
                <Toast.Body>{toastMessage}</Toast.Body>
            </Toast>

        </>
    )

}
