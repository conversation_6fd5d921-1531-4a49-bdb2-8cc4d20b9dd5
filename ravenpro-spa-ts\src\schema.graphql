schema {
  query: Query
  mutation: Mutation
}

type APIKey {
  id: String
  name: String
  owner: String
  status: APIKeyStatus!
}

type ApiKeyAccessRight {
  keyID: String
  accessorEmail: String
}

type ApiKeyRecord {
  id: String
  name: String
  owner: String
  status: String
}

type Document {
  documentId: String
  version: String
  status: DocumentStatus
  startTime: DateTime!
  endTime: FSharpOptionOfDateTime
  updatedBy: String
  uploadUrl: FSharpOptionOfString
}

type DocumentStatusAnnotating {
  current: Int!
  total: Int!
}

type DocumentStatusComplete {
  status: String
}

type DocumentStatusEmbedding {
  current: Int!
  total: Int!
}

type DocumentStatusError {
  errorMessage: String
}

type DocumentStatusSplitting {
  status: String
}

type DocumentStatusStarted {
  status: String
}

type DocumentStatusUploaded {
  status: String
}

type FSharpOptionOfDateTime {
  get_Value: DateTime!
  value: DateTime!
}

type FSharpOptionOfString {
  get_Value: String
  value: String
}

type Mutation {
  createApiKey(name: String, secret: String): APIKey @cost(weight: "10")
  shareApiKey(sha: String, email: String): ApiKeyAccessRight @cost(weight: "10")
  transferApiKey(sha: String, email: String): Boolean! @cost(weight: "10")
  revokeApiKey(sha: String, email: String): Boolean! @cost(weight: "10")
  renameApiKey(sha: String, before: String, after: String): ApiKeyRecord
    @cost(weight: "10")
  deleteApiKey(sha: String): Boolean! @cost(weight: "10")
  importRequirement(jamaIds: [String], apiKeyId: String): RequirementSnapshot
    @cost(weight: "10")
  deleteRequirementSnapshot(snapshotId: String): Boolean! @cost(weight: "10")
  createRequirementTag(text: String): RequirementSnapshotTag
  renameRequirementTag(
    tagId: String
    oldText: String
    newText: String
  ): Boolean!
  deleteRequirementTag(id: String, text: String): Boolean!
  tagRequirementSnapshot(
    snapshotId: String
    tagId: String
    tagText: String
  ): Boolean!
  untagRequirementSnapshot(snapshotId: String, tagId: String): Boolean!
  createVerification(
    documentId: String
    documentVersion: String
    snapshotId: String
    apiKeyId: String
  ): Verification @cost(weight: "10")
  deleteVerification(verificationId: String): Boolean! @cost(weight: "10")
  createVerificationTag(text: String): VerificationTag
  renameVerificationTag(
    tagId: String
    oldText: String
    newText: String
  ): Boolean!
  deleteVerificationTag(id: String, text: String): Boolean!
  tagVerification(
    verificationId: String
    tagId: String
    tagText: String
  ): Boolean!
  untagVerification(verificationId: String, tagId: String): Boolean!
  createDocument(
    documentId: String
    version: String
    apiKeyId: String
  ): Document @cost(weight: "10")
  deleteDocument(documentId: String, version: String): Boolean!
    @cost(weight: "10")
}

type PaginatedOfAPIKey {
  items: [APIKey]
  nextToken: String
}

type PaginatedOfApiKeyAccessRight {
  items: [ApiKeyAccessRight]
  nextToken: String
}

type PaginatedOfDocument {
  items: [Document]
  nextToken: String
}

type PaginatedOfRequirementSnapshot {
  items: [RequirementSnapshot]
  nextToken: String
}

type PaginatedOfRequirementSnapshotTag {
  items: [RequirementSnapshotTag]
  nextToken: String
}

type PaginatedOfVerification {
  items: [Verification]
  nextToken: String
}

type PaginatedOfVerificationTag {
  items: [VerificationTag]
  nextToken: String
}

type Query {
  ownedKeys(token: String, limit: Int!): PaginatedOfAPIKey @cost(weight: "10")
  accessibleKeys(token: String, limit: Int!): PaginatedOfAPIKey
    @cost(weight: "10")
  accessors(
    token: String
    limit: Int!
    sha: String
  ): PaginatedOfApiKeyAccessRight @cost(weight: "10")
  secret(sha: String): String @cost(weight: "10")
  apiKey(sha: String): APIKey @cost(weight: "10")
  requirementSnapshots(
    token: String
    limit: Int!
    mineOnly: Boolean!
    statusFilter: RequirementSnapshotStatusFilter!
    first: DateTime!
    last: DateTime!
  ): PaginatedOfRequirementSnapshot @cost(weight: "10")
  requirementSnapshot(snapshotId: String): RequirementSnapshot
  snapshotsWithTag(
    token: String
    limit: Int!
    tag: String
    first: DateTime!
    last: DateTime!
  ): PaginatedOfRequirementSnapshot
  requirementTag(tagId: String, text: String): RequirementSnapshotTag
  requirementTags(
    token: String
    limit: Int!
    prefix: String
  ): PaginatedOfRequirementSnapshotTag
  verifications(
    token: String
    limit: Int!
    first: DateTime!
    last: DateTime!
    mineOnly: Boolean!
    statusFilter: VerificationStatusFilter!
  ): PaginatedOfVerification @cost(weight: "10")
  verification(verificationId: String): Verification
  verificationsWithTag(
    token: String
    limit: Int!
    tag: String
    first: DateTime!
    last: DateTime!
  ): PaginatedOfVerification
  verificationTag(tagId: String, text: String): VerificationTag
  verificationTags(
    token: String
    limit: Int!
    prefix: String
  ): PaginatedOfVerificationTag
  verificationReportUrl(verificationId: String): String
  document(documentId: String, version: String): Document
  documents(
    token: String
    limit: Int!
    mineOnly: Boolean!
    status: DocumentStatusFilter!
    first: DateTime!
    last: DateTime!
  ): PaginatedOfDocument @cost(weight: "10")
}

type RequirementSnapshot {
  id: String
  author: String
  createdAt: DateTime!
  status: RequirementSnapshotStatus
  downloadUrl: String
  tags: [RequirementSnapshotTag]
}

type RequirementSnapshotStatusDeleting {
  status: String
}

type RequirementSnapshotStatusDone {
  status: String
}

type RequirementSnapshotStatusError {
  errorMessage: String
}

type RequirementSnapshotStatusFetching {
  completed: Int!
  total: Int!
}

type RequirementSnapshotStatusProcessing {
  completed: Int!
  total: Int!
}

type RequirementSnapshotStatusStarting {
  status: String
}

type RequirementSnapshotTag {
  id: String
  text: String
  createdAt: DateTime!
}

type TaskStatusDone {
  status: String
}

type TaskStatusError {
  errorMessage: String
}

type TaskStatusPending {
  status: String
}

type TaskStatusProcessing {
  completed: Int!
  total: Int!
}

type Verification {
  id: String
  createdBy: String
  createdAt: DateTime!
  status: VerificationStatus
  documentId: String
  documentVersion: String
  snapshotId: String
  rankingStatus: TaskStatus
  verifyingStatus: TaskStatus
  reportingStatus: TaskStatus
}

type VerificationStatusDeleting {
  status: String
}

type VerificationStatusDone {
  status: String
}

type VerificationStatusError {
  status: String
}

type VerificationStatusRunning {
  status: String
}

type VerificationTag {
  id: String
  text: String
  createdAt: DateTime!
}

union DocumentStatus =
  | DocumentStatusStarted
  | DocumentStatusUploaded
  | DocumentStatusSplitting
  | DocumentStatusAnnotating
  | DocumentStatusEmbedding
  | DocumentStatusComplete
  | DocumentStatusError

union RequirementSnapshotStatus =
  | RequirementSnapshotStatusStarting
  | RequirementSnapshotStatusFetching
  | RequirementSnapshotStatusProcessing
  | RequirementSnapshotStatusDone
  | RequirementSnapshotStatusError
  | RequirementSnapshotStatusDeleting

union TaskStatus =
  | TaskStatusPending
  | TaskStatusProcessing
  | TaskStatusDone
  | TaskStatusError

union VerificationStatus =
  | VerificationStatusRunning
  | VerificationStatusDone
  | VerificationStatusError
  | VerificationStatusDeleting

enum APIKeyStatus {
  ACTIVE
  DELETING
  UNKNOWN
}

enum DocumentStatusFilter {
  RUNNING
  COMPLETE
  ERROR
  ALL
}

enum RequirementSnapshotStatusFilter {
  ALL
  RUNNING
  COMPLETED
  FAILED
}

enum VerificationStatusFilter {
  ALL
  RUNNING
  DONE
  ERROR
}

"The purpose of the `cost` directive is to define a `weight` for GraphQL types, fields, and arguments. Static analysis can use these weights when calculating the overall cost of a query or response."
directive @cost(
  "The `weight` argument defines what value to add to the overall cost for every appearance, or possible appearance, of a type, field, argument, etc."
  weight: String!
) on SCALAR | OBJECT | FIELD_DEFINITION | ARGUMENT_DEFINITION | ENUM | INPUT_FIELD_DEFINITION

"The `@specifiedBy` directive is used within the type system definition language to provide a URL for specifying the behavior of custom scalar definitions."
directive @specifiedBy(
  "The specifiedBy URL points to a human-readable specification. This field will only read a result for scalar types."
  url: String!
) on SCALAR

"The `DateTime` scalar represents an ISO-8601 compliant date time type."
scalar DateTime @specifiedBy(url: "https://www.graphql-scalars.com/date-time")
