import React, { useState, type ChangeEvent, type FormEvent, useEffect } from 'react';
import { Modal, Button, Form, Badge, CloseButton, Alert, InputGroup } from 'react-bootstrap';
import { useDeleteRequirementTagMutation, useGetRequirementTagsLazyQuery, useUntagRequirementSnapshotMutation } from '../../../gql/graphql';

interface TagModalProps {
    open: boolean;
    onClose: (state: boolean) => void;
}

type MessageType = 'success' | 'danger' | 'warning' | null;

export const ViewTagsModal: React.FC<TagModalProps> = ({ open, onClose }) => {

    const [availableTags, setAvailableTags] = useState<string[]>([]);
    const [tags, setTags] = useState<string[]>([]);
    const [newTag, setNewTag] = useState<string>('');
    const [message, setMessage] = useState<string | null>(null);
    const [messageType, setMessageType] = useState<MessageType>(null);

    const TOKENLIMIT = 100;

    const [fetchTags, { loading: tagsLoading, data: tagsData, error: tagsError }] = useGetRequirementTagsLazyQuery();
    const [deleteTag, { loading: deleteTagLoading }] = useDeleteRequirementTagMutation();
    const [addTag, { loading: addTagLoading, error: addTagError }] = useUntagRequirementSnapshotMutation()

    const handleAddTag = () => {
        const trimmed = newTag.trim();

        if (!trimmed) {
            showMessage('Tag cannot be empty.', 'danger');
            return;
        }

        if (tags.includes(trimmed)) {
            showMessage(`"${trimmed}" already exists.`, 'danger');
            return;
        }

        setTags([...tags, trimmed]);
        showMessage(`Added "${trimmed}"`, 'success');
        setNewTag('');
    };

    const handleDeleteTag = (tagToDelete: string) => {
        setTags(tags.filter(tag => tag !== tagToDelete));
        showMessage(`Deleted "${tagToDelete}"`, 'warning');
    };

    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        setNewTag(e.target.value);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            e.preventDefault(); // prevent form submit or newline
            handleAddTag();
        }
    };

    const showMessage = (msg: string, type: MessageType) => {
        setMessage(msg);
        setMessageType(type);
        setTimeout(() => {
            setMessage(null);
            setMessageType(null);
        }, 3000);
    };

    useEffect(() => {
        if (open) {
            setNewTag('');
            setMessage(null);
            setMessageType(null);
        }
    }, [open]);

    const handleCloseModal = (() => {
        onClose(false);
    });

    return (
        <Modal show={open} onHide={handleCloseModal} centered>
            <Modal.Header closeButton>
                <Modal.Title>Manage Tags</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {message && <Alert variant={messageType || 'info'}>{message}</Alert>}

                <div className="mb-3">
                    {tags.length > 0 ? (
                        tags.map(tag => (
                            <Badge
                                key={tag}
                                bg="warning"
                                text="dark"
                                className="m-1"
                            >
                                {tag}{' '}
                                <CloseButton
                                    onClick={() => handleDeleteTag(tag)}
                                    style={{ marginLeft: '0.5rem', fontSize: '0.7rem' }}
                                />
                            </Badge>
                        ))
                    ) : (
                        <div>No tags available.</div>
                    )}
                </div>

                <Form.Group controlId="formNewTag">
                    <Form.Label>Add New Tag or Select from existing tags</Form.Label>
                    <InputGroup>
                        <Form.Control
                            type="text"
                            list="tagSuggestions"
                            value={newTag}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            placeholder="Type or select a tag"
                        />
                        <datalist id="tagSuggestions">
                            {availableTags
                                .filter(s => !tags.includes(s)) // don't suggest tags already added
                                .map(availableTags => (
                                    <option key={availableTags} value={availableTags} />
                                ))}
                        </datalist>                        
                        <Button variant="dark" onClick={handleAddTag}>
                            + Add
                        </Button>
                    </InputGroup>
                </Form.Group>
            </Modal.Body>
            <Modal.Footer>
                <Button variant="secondary" onClick={handleCloseModal}>
                    Close
                </Button>
            </Modal.Footer>
        </Modal>
    );
};