
import type { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
  overwrite: true,
  schema: "./src/schema.graphql",
  documents: "graphql/**/*.graphql",
  ignoreNoDocuments: true,
  generates: {
    "src/gql/graphql.tsx": {
      plugins: [
        "typescript",
        "typescript-operations",
        "typescript-react-apollo"
      ],
      config: {
        withHooks: true,
        withLazyQuery: true,
        withComponent: false,
        useTypeImports: true,
        withTypename: false
      }
    }
  }
};

export default config;
