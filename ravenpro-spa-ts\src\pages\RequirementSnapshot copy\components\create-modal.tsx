import { useState, type ChangeEvent } from "react";
import { Button, Form, Modal, Toast } from "react-bootstrap";
import { useImportRequirementMutation } from "../../../gql/graphql";

interface CreateModalProps {
    open: boolean;
    onClose: (state:boolean) => void;
    handleRefetch: () => void;
}

export default function CreateModal({ open, onClose, handleRefetch }: CreateModalProps) {

    const [showToast, setShowToast] = useState<boolean>(false);
    const [toastMessage, setToastMessage] = useState<string>("");
    const [inputText, setInputText] = useState('');
    const [parsedList, setParsedList] = useState<string[]>([]);

    const [createSnapshot, { loading }] = useImportRequirementMutation();

    const handleCreate = ( async () => {
        try {
            await createSnapshot({
                variables: {
                    jamaIds: parsedList
                }
            });
            setToastMessage(`Requirements imported successfully!`);
            // to remove after debug
            await new Promise((resolve) => setTimeout(resolve, 5000));
            handleCloseModal();
            setShowToast(true);
            handleRefetch();
        } catch (e: any) {
            console.error(e);
            setToastMessage(String(e));
            handleCloseModal();
            setShowToast(true);
        }
    });

    const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
        const value = e.target.value;
        setInputText(value);

        const list: string[] = value
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

        setParsedList(list);
    };  

    const handleCloseModal = (() => {
        onClose(false);
        setInputText('');
        setParsedList([]);
    });

    return (
        <>
        <Modal show={open} onHide={handleCloseModal} centered>
            <Modal.Header closeButton>
                <div>
                    <h5 className="mb-0">Import JAMA IDs</h5>
                    <small className="text-muted">Key in the JAMA IDs separated by a new row for each document.</small>
                </div>
            </Modal.Header>

            <Form>
                <Modal.Body>
                    <Form.Group className="mb-2" controlId="multi">
                        <Form.Label>Enter one ID per line:</Form.Label>
                        <Form.Control
                        as="textarea"
                        rows={5}
                        value={inputText}
                        onChange={handleChange}
                        placeholder="Enter one ID per line..."
                        />
                    </Form.Group>
    <Form.Text className="text-muted">
        JAMA IDs : {JSON.stringify(parsedList)}
      </Form.Text>

                </Modal.Body>
                <Modal.Footer>
                    <Button variant="light" onClick={handleCloseModal}>
                        Cancel
                    </Button>
                    <Button onClick={handleCreate} variant="dark" disabled={loading || parsedList.length === 0}>
                        {loading ? "Importing Requirements.." : "Import Requirements"}
                    </Button>
                </Modal.Footer>
            </Form>
        </Modal>
        <Toast
            onClose={() => setShowToast(false)}
            show={showToast}
            delay={5000}
            autohide
            style={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            minWidth: '200px',
            }}
        >
            <Toast.Header>
            <strong className="me-auto">Notification</strong>
            </Toast.Header>
            <Toast.Body>{toastMessage}</Toast.Body>
        </Toast>

        </>
    )

}
