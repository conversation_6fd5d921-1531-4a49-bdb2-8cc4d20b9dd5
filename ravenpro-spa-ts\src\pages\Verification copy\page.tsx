import { useState, useMemo } from "react";
import {
    <PERSON><PERSON>er,
    <PERSON><PERSON>,
    But<PERSON>,
    Table,
    Form,
    Modal,
    Badge,
    ProgressBar,
    Row,
    Col,
    InputGroup,
    Alert,
    Card,
    ListGroup,
    Nav
} from "react-bootstrap";
import {
    PiSealCheck,
    PiTrash,
    PiPlus,
    PiTag,
    PiCalendar,
    PiArrowLeft,
    PiArrowRight,
    PiCheck,
    PiFileText,
    PiKey,
    PiDatabase
} from "react-icons/pi";
import type {
    Verification,
    VerificationTag,
    VerificationSearchFilters,
    VerificationSearchMode,
    VerificationStatus,
    ProcessingStatus,
    RequirementSnapshot,
    UserManualPreprocessing,
    APIKey,
    VerificationWizardStep,
    VerificationWizardData
} from "../../types/types";
import {
    useGetVerificationsQuery,
    useCreateVerificationMutation,
    useDeleteVerificationMutation,
    useGetRequirementSnapshotsQuery,
    useGetDocumentsQuery,
    useGetOwnedKeysQuery,
    useGetVerificationTagsLazyQuery,
    useCreateVerificationTagMutation,
    useRenameVerificationTagMutation,
    useDeleteVerificationTagMutation,
    VerificationStatusFilter,
    RequirementSnapshotStatusFilter,
    DocumentStatusFilter,
    type Verification as GraphQLVerification,
    type RequirementSnapshot as GraphQLRequirementSnapshot,
    type Document as GraphQLDocument,
    type ApiKey as GraphQLApiKey
} from "../../gql/graphql";
import TagManager from "../../components/TagManager";

// Mock data for demonstration
const mockVerificationTags: VerificationTag[] = [
    { id: "1", name: "Critical" },
    { id: "2", name: "High Priority" },
    { id: "3", name: "API Documentation" },
    { id: "4", name: "User Interface" },
    { id: "5", name: "Security" }
];

const mockVerifications: Verification[] = [
    {
        id: "VER-001",
        createdAt: "2024-01-15T10:30:00Z",
        createdBy: "John Doe",
        documentId: "DOC-123",
        documentVersion: "v1.2",
        rankingStatus: "Done",
        reportingStatus: "Processing",
        snapshotId: "SNAP-456",
        status: "Running",
        verifyingStatus: "Done",
        tags: [mockVerificationTags[0], mockVerificationTags[1]],
        progress: 75
    },
    {
        id: "VER-002",
        createdAt: "2024-01-14T14:20:00Z",
        createdBy: "Jane Smith",
        documentId: "DOC-124",
        documentVersion: "v2.0",
        rankingStatus: "Done",
        reportingStatus: "Done",
        snapshotId: "SNAP-457",
        status: "Done",
        verifyingStatus: "Done",
        tags: [mockVerificationTags[2]]
    },
    {
        id: "VER-003",
        createdAt: "2024-01-13T09:15:00Z",
        createdBy: "Bob Johnson",
        documentId: "DOC-125",
        documentVersion: "v1.0",
        rankingStatus: "Error",
        reportingStatus: "Pending",
        snapshotId: "SNAP-458",
        status: "Error",
        verifyingStatus: "Error",
        tags: [mockVerificationTags[3], mockVerificationTags[4]]
    },
    {
        id: "VER-004",
        createdAt: "2024-01-12T16:45:00Z",
        createdBy: "Alice Brown",
        documentId: "DOC-126",
        documentVersion: "v1.1",
        rankingStatus: "Processing",
        reportingStatus: "Pending",
        snapshotId: "SNAP-459",
        status: "Running",
        verifyingStatus: "Processing",
        tags: [mockVerificationTags[1]],
        progress: 45
    }
];

// Mock data for wizard steps
const mockRequirementSnapshots: RequirementSnapshot[] = [
    {
        id: "SNAP-001",
        name: "User Authentication Requirements",
        status: "DONE",
        createdAt: "2024-01-10T10:00:00Z",
        description: "Complete set of authentication and authorization requirements"
    },
    {
        id: "SNAP-002",
        name: "API Security Requirements",
        status: "DONE",
        createdAt: "2024-01-09T15:30:00Z",
        description: "Security requirements for REST API endpoints"
    },
    {
        id: "SNAP-003",
        name: "Data Privacy Requirements",
        status: "DONE",
        createdAt: "2024-01-08T11:20:00Z",
        description: "GDPR and data privacy compliance requirements"
    }
];

const mockUserManualPreprocessings: UserManualPreprocessing[] = [
    {
        id: "UMP-001",
        name: "User Guide v2.1",
        status: "DONE",
        createdAt: "2024-01-11T09:00:00Z",
        fileName: "user-guide-v2.1.pdf"
    },
    {
        id: "UMP-002",
        name: "API Documentation",
        status: "DONE",
        createdAt: "2024-01-10T14:15:00Z",
        fileName: "api-docs.docx"
    },
    {
        id: "UMP-003",
        name: "Installation Manual",
        status: "DONE",
        createdAt: "2024-01-09T16:45:00Z",
        fileName: "installation-guide.txt"
    }
];

const mockAPIKeys: APIKey[] = [
    {
        id: "API-001",
        name: "OpenAI GPT-4",
        type: "Shared",
        description: "Shared OpenAI API key for general use",
        isActive: true
    },
    {
        id: "API-002",
        name: "Personal Claude API",
        type: "Owned",
        description: "Personal Anthropic Claude API key",
        isActive: true
    },
    {
        id: "API-003",
        name: "Team Gemini Key",
        type: "Shared",
        description: "Team Google Gemini API key",
        isActive: true
    },
    {
        id: "API-004",
        name: "Legacy GPT-3.5",
        type: "Owned",
        description: "Personal OpenAI GPT-3.5 key (deprecated)",
        isActive: false
    }
];

function VerificationPage() {
    // GraphQL hooks
    const { data: verificationsData, loading: verificationsLoading, error: verificationsError, refetch: refetchVerifications } = useGetVerificationsQuery({
        variables: {
            token: undefined,
            limit: 50,
            first: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year ago
            last: new Date().toISOString(),
            mineOnly: true,
            statusFilter: VerificationStatusFilter.All
        }
    });

    const { data: snapshotsData, loading: snapshotsLoading } = useGetRequirementSnapshotsQuery({
        variables: {
            token: undefined,
            limit: 50,
            mineOnly: false,
            statusFilter: RequirementSnapshotStatusFilter.Completed,
            first: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
            last: new Date().toISOString()
        }
    });

    const { data: documentsData, loading: documentsLoading } = useGetDocumentsQuery({
        variables: {
            token: undefined,
            limit: 50,
            mineOnly: true,
            status: DocumentStatusFilter.Complete,
            first: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
            last: new Date().toISOString()
        }
    });

    const { data: apiKeysData, loading: apiKeysLoading } = useGetOwnedKeysQuery({
        variables: {
            token: undefined,
            limit: 50
        }
    });

    const [createVerification] = useCreateVerificationMutation();
    const [deleteVerification] = useDeleteVerificationMutation();

    // Verification Tag Management
    const [fetchVerificationTags, { loading: verificationTagsLoading, error: verificationTagsError, data: verificationTagsData }] = useGetVerificationTagsLazyQuery();
    const [createVerificationTag, { loading: createVerificationTagLoading, error: createVerificationTagError }] = useCreateVerificationTagMutation();
    const [renameVerificationTag, { loading: renameVerificationTagLoading, error: renameVerificationTagError }] = useRenameVerificationTagMutation();
    const [deleteVerificationTag, { loading: deleteVerificationTagLoading, error: deleteVerificationTagError }] = useDeleteVerificationTagMutation();

    // Local state
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showTagModal, setShowTagModal] = useState(false);
    const [selectedVerification, setSelectedVerification] = useState<Verification | null>(null);
    const [searchFilters, setSearchFilters] = useState<VerificationSearchFilters>({
        mode: 'mine',
        mineOnly: false,
        statusFilter: [],
        firstDate: '',
        lastDate: ''
    });
    const [editingTag, setEditingTag] = useState<VerificationTag | null>(null);
    const [newTagName, setNewTagName] = useState('');
    const [showTagManager, setShowTagManager] = useState(false);

    // Wizard state
    const [currentStep, setCurrentStep] = useState<VerificationWizardStep>('snapshot');
    const [wizardData, setWizardData] = useState<VerificationWizardData>({});

    // Convert GraphQL data to local types
    const verifications = useMemo(() => {
        if (!verificationsData?.verifications?.items) return [];
        return verificationsData.verifications.items
            .filter((v): v is GraphQLVerification => v !== null)
            .map((v): Verification => ({
                id: v.id || '',
                createdAt: v.createdAt,
                createdBy: v.createdBy || 'Unknown',
                documentId: v.documentId || '',
                documentVersion: v.documentVersion || '',
                rankingStatus: 'Pending', // TODO: Convert from GraphQL TaskStatus
                reportingStatus: 'Pending', // TODO: Convert from GraphQL TaskStatus
                snapshotId: v.snapshotId || '',
                status: 'Running', // TODO: Convert from GraphQL VerificationStatus
                verifyingStatus: 'Processing', // TODO: Convert from GraphQL TaskStatus
                tags: [], // TODO: Implement verification tags
                progress: 0 // TODO: Calculate from task statuses
            }));
    }, [verificationsData]);

    const requirementSnapshots = useMemo(() => {
        if (!snapshotsData?.requirementSnapshots?.items) return [];
        return snapshotsData.requirementSnapshots.items
            .filter((s): s is GraphQLRequirementSnapshot => s !== null)
            .map((s): RequirementSnapshot => ({
                id: s.id || '',
                name: `Snapshot ${s.id}`,
                status: 'DONE',
                createdAt: s.createdAt,
                description: `Created by ${s.author}`
            }));
    }, [snapshotsData]);

    const userManualPreprocessings = useMemo(() => {
        if (!documentsData?.documents?.items) return [];
        return documentsData.documents.items
            .filter((d): d is GraphQLDocument => d !== null)
            .map((d): UserManualPreprocessing => ({
                id: d.documentId || '',
                name: `Document ${d.documentId}`,
                status: 'DONE',
                createdAt: d.startTime,
                fileName: `${d.documentId}-${d.version}`
            }));
    }, [documentsData]);

    const apiKeys = useMemo(() => {
        if (!apiKeysData?.ownedKeys?.items) return [];
        return apiKeysData.ownedKeys.items
            .filter((k): k is GraphQLApiKey => k !== null)
            .map((k): APIKey => ({
                id: k.id || '',
                name: k.name || 'Unnamed Key',
                type: 'Owned',
                description: `Owner: ${k.owner}`,
                isActive: k.status === 'ACTIVE'
            }));
    }, [apiKeysData]);

    // Load verification tags
    const loadVerificationTags = () => {
        fetchVerificationTags({
            variables: {
                token: undefined,
                limit: 50,
                prefix: undefined
            }
        });
    };

    // Tag Management Handlers for TagManager component
    const handleCreateVerificationTagManager = async (text: string) => {
        await createVerificationTag({
            variables: { text }
        });
        loadVerificationTags(); // Refresh tags list
    };

    const handleUpdateVerificationTagManager = async (tag: any, newText: string) => {
        await renameVerificationTag({
            variables: {
                tagId: tag.id,
                oldText: tag.text,
                newText
            }
        });
        loadVerificationTags(); // Refresh tags list
    };

    const handleDeleteVerificationTagManager = async (tag: any) => {
        await deleteVerificationTag({
            variables: {
                id: tag.id,
                text: tag.text
            }
        });
        loadVerificationTags(); // Refresh tags list
    };

    // Format date from ISO to dd/mm/yyyy-hh:mm
    const formatDate = (isoDate: string): string => {
        const date = new Date(isoDate);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${day}/${month}/${year}-${hours}:${minutes}`;
    };

    // Get status badge variant
    const getStatusVariant = (status: VerificationStatus | ProcessingStatus): string => {
        switch (status) {
            case 'Done': return 'success';
            case 'Processing':
            case 'Running': return 'primary';
            case 'Pending': return 'info';
            case 'Error': return 'danger';
            case 'Deleting': return 'warning';
            default: return 'secondary';
        }
    };

    // Check if status should show progress bar
    const shouldShowProgress = (status: VerificationStatus | ProcessingStatus): boolean => {
        return status === 'Processing' || status === 'Running';
    };

    // Filter verifications based on search criteria
    const filteredVerifications = useMemo(() => {
        return verifications.filter(verification => {
            // Mode-specific filters
            if (searchFilters.mode === 'mine') {
                if (searchFilters.mineOnly && verification.createdBy !== 'Current User') {
                    return false;
                }
                if (searchFilters.statusFilter && searchFilters.statusFilter.length > 0) {
                    if (!searchFilters.statusFilter.includes(verification.status)) {
                        return false;
                    }
                }
            } else if (searchFilters.mode === 'tags') {
                if (searchFilters.tag) {
                    const hasTag = verification.tags.some(tag =>
                        tag.name.toLowerCase().includes(searchFilters.tag!.toLowerCase())
                    );
                    if (!hasTag) return false;
                }
            }

            // Date filters
            if (searchFilters.firstDate) {
                const verificationDate = new Date(verification.createdAt);
                const firstDate = new Date(searchFilters.firstDate);
                if (verificationDate < firstDate) return false;
            }

            if (searchFilters.lastDate) {
                const verificationDate = new Date(verification.createdAt);
                const lastDate = new Date(searchFilters.lastDate);
                if (verificationDate > lastDate) return false;
            }

            return true;
        });
    }, [verifications, searchFilters]);

    // Handle delete verification
    const handleDelete = (id: string) => {
        if (window.confirm('Are you sure you want to delete this verification?')) {
            setVerifications(prev => prev.filter(verification => verification.id !== id));
        }
    };

    // Handle tag operations
    const handleTagEdit = (verification: Verification) => {
        setSelectedVerification(verification);
        setShowTagModal(true);
        setTagModalMode('view');
    };

    const handleAddTag = () => {
        if (!newTagName.trim() || !selectedVerification) return;

        const newTag: VerificationTag = {
            id: Date.now().toString(),
            name: newTagName.trim()
        };

        setVerifications(prev => prev.map(verification =>
            verification.id === selectedVerification.id
                ? { ...verification, tags: [...verification.tags, newTag] }
                : verification
        ));

        setNewTagName('');
        setTagModalMode('view');
    };

    const handleDeleteTag = (tagId: string) => {
        if (!selectedVerification) return;

        setVerifications(prev => prev.map(verification =>
            verification.id === selectedVerification.id
                ? { ...verification, tags: verification.tags.filter(tag => tag.id !== tagId) }
                : verification
        ));
    };

    const handleRenameTag = (tagId: string, newName: string) => {
        if (!selectedVerification || !newName.trim()) return;

        setVerifications(prev => prev.map(verification =>
            verification.id === selectedVerification.id
                ? {
                    ...verification,
                    tags: verification.tags.map(tag =>
                        tag.id === tagId ? { ...tag, name: newName.trim() } : tag
                    )
                }
                : verification
        ));

        setEditingTag(null);
    };

    // Wizard navigation
    const getStepIndex = (step: VerificationWizardStep): number => {
        const steps: VerificationWizardStep[] = ['snapshot', 'manual', 'apikey', 'confirm'];
        return steps.indexOf(step);
    };

    const canGoNext = (): boolean => {
        switch (currentStep) {
            case 'snapshot': return !!wizardData.selectedSnapshot;
            case 'manual': return !!wizardData.selectedManual;
            case 'apikey': return !!wizardData.selectedAPIKey;
            case 'confirm': return true;
            default: return false;
        }
    };

    const handleNext = () => {
        const steps: VerificationWizardStep[] = ['snapshot', 'manual', 'apikey', 'confirm'];
        const currentIndex = getStepIndex(currentStep);
        if (currentIndex < steps.length - 1) {
            setCurrentStep(steps[currentIndex + 1]);
        }
    };

    const handlePrevious = () => {
        const steps: VerificationWizardStep[] = ['snapshot', 'manual', 'apikey', 'confirm'];
        const currentIndex = getStepIndex(currentStep);
        if (currentIndex > 0) {
            setCurrentStep(steps[currentIndex - 1]);
        }
    };

    const handleStartVerification = async () => {
        if (!wizardData.selectedSnapshot || !wizardData.selectedManual || !wizardData.selectedAPIKey) {
            return;
        }

        try {
            await createVerification({
                variables: {
                    documentId: wizardData.selectedManual.id,
                    documentVersion: 'v1.0',
                    snapshotId: wizardData.selectedSnapshot.id
                }
            });

            // Refetch verifications to get the updated list
            await refetchVerifications();

            setShowCreateModal(false);
            setCurrentStep('snapshot');
            setWizardData({});
        } catch (error) {
            console.error('Error creating verification:', error);
            alert('Failed to create verification. Please try again.');
        }
    };

    const resetWizard = () => {
        setCurrentStep('snapshot');
        setWizardData({});
    };

    const testWss = () => {
        var socket = "wss://frkkgud75zgnhkiopi2zuvzkya.appsync-realtime-api.ap-southeast-2.amazonaws.com";
        var ws = new WebSocket(socket);
        ws.onopen = function () {
            console.log("Connection opened");
            ws.send(JSON.stringify({
                "id": "1",
                "type": "start",
                "payload": {
                    "data": {
                        "query": "subscription MySubscription {\n  onVerificationUpdate {\n    id\n    status\n    __typename\n  }\n}\n",
                        "variables": null
                    }
                }
            }));
        };
        ws.onmessage = function (event) {
            console.log("Received: " + event.data);
        };
        ws.onerror = function (event) {
            console.log("Error: " + event);
        };
        ws.onclose = function (event) {
            console.log("Connection closed");
        };
        
    }

    return (
        <>
            <Container className="mt-4">
                {/* Header */}
                <Stack direction="horizontal" gap={2} className="mb-4">
                    <div className="p-2">
                        <h1><PiSealCheck /> Verification</h1>
                        <p className="text-muted">Use GenAI to verify your user manual against the JAMA requirements.</p>
                    </div>
                    <div className="p-2 ms-auto d-flex gap-2">
                        <Button
                            variant="outline-primary"
                            onClick={() => setShowTagManager(true)}
                            className="d-flex align-items-center gap-2"
                        >
                            <PiTag /> Tag Manager
                        </Button>
                        <Button variant="dark" onClick={() => setShowCreateModal(true)}>
                            <PiPlus /> New Verification
                        </Button>
                    </div>
                </Stack>

                {/* Search Bar */}
                <Card className="mb-4">
                    <Card.Body>
                        <Row className="align-items-end">
                            <Col md={3}>
                                <Form.Label>Search Mode</Form.Label>
                                <Form.Select
                                    value={searchFilters.mode}
                                    onChange={(e) => setSearchFilters(prev => ({
                                        ...prev,
                                        mode: e.target.value as VerificationSearchMode,
                                        mineOnly: false,
                                        statusFilter: [],
                                        tag: ''
                                    }))}
                                >
                                    <option value="mine">Mine Only</option>
                                    <option value="tags">Tags</option>
                                </Form.Select>
                            </Col>

                            {searchFilters.mode === 'mine' ? (
                                <>
                                    <Col md={2}>
                                        <Form.Check
                                            type="checkbox"
                                            label="Mine Only"
                                            checked={searchFilters.mineOnly || false}
                                            onChange={(e) => setSearchFilters(prev => ({
                                                ...prev,
                                                mineOnly: e.target.checked
                                            }))}
                                        />
                                    </Col>
                                    <Col md={3}>
                                        <Form.Label>Status Filter</Form.Label>
                                        <Form.Select
                                            multiple
                                            value={searchFilters.statusFilter || []}
                                            onChange={(e) => {
                                                const values = Array.from(e.target.selectedOptions, option => option.value) as VerificationStatus[];
                                                setSearchFilters(prev => ({ ...prev, statusFilter: values }));
                                            }}
                                        >
                                            <option value="Done">Done</option>
                                            <option value="Running">Running</option>
                                            <option value="Error">Error</option>
                                            <option value="Deleting">Deleting</option>
                                        </Form.Select>
                                    </Col>
                                </>
                            ) : (
                                <Col md={3}>
                                    <Form.Label>Tag Search</Form.Label>
                                    <InputGroup>
                                        <InputGroup.Text><PiTag /></InputGroup.Text>
                                        <Form.Control
                                            type="text"
                                            placeholder="Search by tag..."
                                            value={searchFilters.tag || ''}
                                            onChange={(e) => setSearchFilters(prev => ({
                                                ...prev,
                                                tag: e.target.value
                                            }))}
                                        />
                                    </InputGroup>
                                </Col>
                            )}

                            <Col md={2}>
                                <Form.Label>First Date</Form.Label>
                                <InputGroup>
                                    <InputGroup.Text><PiCalendar /></InputGroup.Text>
                                    <Form.Control
                                        type="date"
                                        value={searchFilters.firstDate || ''}
                                        onChange={(e) => setSearchFilters(prev => ({
                                            ...prev,
                                            firstDate: e.target.value
                                        }))}
                                    />
                                </InputGroup>
                            </Col>

                            <Col md={2}>
                                <Form.Label>Last Date</Form.Label>
                                <InputGroup>
                                    <InputGroup.Text><PiCalendar /></InputGroup.Text>
                                    <Form.Control
                                        type="date"
                                        value={searchFilters.lastDate || ''}
                                        onChange={(e) => setSearchFilters(prev => ({
                                            ...prev,
                                            lastDate: e.target.value
                                        }))}
                                    />
                                </InputGroup>
                            </Col>
                        </Row>
                    </Card.Body>
                </Card>

                {/* Main Table */}
                <Card>
                    <Card.Body>
                        <Table responsive striped hover>
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Created At</th>
                                    <th>Created By</th>
                                    <th>Document ID</th>
                                    <th>Document Version</th>
                                    <th>Snapshot ID</th>
                                    <th>Status</th>
                                    <th>Ranking Status</th>
                                    <th>Reporting Status</th>
                                    <th>Verifying Status</th>
                                    <th>Tags</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredVerifications.length === 0 ? (
                                    <tr>
                                        <td colSpan={12} className="text-center text-muted py-4">
                                            No verifications found matching your criteria.
                                        </td>
                                    </tr>
                                ) : (
                                    filteredVerifications.map((verification) => (
                                        <tr key={verification.id}>
                                            <td>
                                                <code>{verification.id}</code>
                                            </td>
                                            <td>{formatDate(verification.createdAt)}</td>
                                            <td>{verification.createdBy}</td>
                                            <td>
                                                <code>{verification.documentId}</code>
                                            </td>
                                            <td>
                                                <Badge bg="info">{verification.documentVersion}</Badge>
                                            </td>
                                            <td>
                                                <code>{verification.snapshotId}</code>
                                            </td>
                                            <td>
                                                <Badge bg={getStatusVariant(verification.status)}>
                                                    {verification.status}
                                                </Badge>
                                                {shouldShowProgress(verification.status) && verification.progress !== undefined && (
                                                    <div className="mt-1">
                                                        <ProgressBar
                                                            now={verification.progress}
                                                            label={`${verification.progress}%`}
                                                            style={{ height: '15px' }}
                                                        />
                                                    </div>
                                                )}
                                            </td>
                                            <td>
                                                <Badge bg={getStatusVariant(verification.rankingStatus)}>
                                                    {verification.rankingStatus}
                                                </Badge>
                                                {shouldShowProgress(verification.rankingStatus) && verification.progress !== undefined && (
                                                    <div className="mt-1">
                                                        <ProgressBar
                                                            now={verification.progress}
                                                            label={`${verification.progress}%`}
                                                            style={{ height: '15px' }}
                                                        />
                                                    </div>
                                                )}
                                            </td>
                                            <td>
                                                <Badge bg={getStatusVariant(verification.reportingStatus)}>
                                                    {verification.reportingStatus}
                                                </Badge>
                                                {shouldShowProgress(verification.reportingStatus) && verification.progress !== undefined && (
                                                    <div className="mt-1">
                                                        <ProgressBar
                                                            now={verification.progress}
                                                            label={`${verification.progress}%`}
                                                            style={{ height: '15px' }}
                                                        />
                                                    </div>
                                                )}
                                            </td>
                                            <td>
                                                <Badge bg={getStatusVariant(verification.verifyingStatus)}>
                                                    {verification.verifyingStatus}
                                                </Badge>
                                                {shouldShowProgress(verification.verifyingStatus) && verification.progress !== undefined && (
                                                    <div className="mt-1">
                                                        <ProgressBar
                                                            now={verification.progress}
                                                            label={`${verification.progress}%`}
                                                            style={{ height: '15px' }}
                                                        />
                                                    </div>
                                                )}
                                            </td>
                                            <td>
                                                <div className="d-flex flex-wrap gap-1">
                                                    {verification.tags.map((tag) => (
                                                        <Badge
                                                            key={tag.id}
                                                            bg="secondary"
                                                            style={{ cursor: 'pointer' }}
                                                            onClick={() => handleTagEdit(verification)}
                                                        >
                                                            {tag.name}
                                                        </Badge>
                                                    ))}
                                                    {verification.tags.length === 0 && (
                                                        <Button
                                                            variant="outline-secondary"
                                                            size="sm"
                                                            onClick={() => handleTagEdit(verification)}
                                                        >
                                                            <PiTag /> Add Tags
                                                        </Button>
                                                    )}
                                                </div>
                                            </td>
                                            <td>
                                                <Button
                                                    variant="outline-danger"
                                                    size="sm"
                                                    onClick={() => handleDelete(verification.id)}
                                                    disabled={verification.status === 'Deleting'}
                                                >
                                                    <PiTrash />
                                                </Button>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </Table>
                    </Card.Body>
                </Card>
            </Container>

            {/* Create New Verification Wizard Modal */}
            <Modal show={showCreateModal} onHide={() => setShowCreateModal(false)} size="xl">
                <Modal.Header closeButton>
                    <Modal.Title>New Verification - Step {getStepIndex(currentStep) + 1} of 4</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {/* Step Navigation */}
                    <Nav variant="pills" className="mb-4">
                        <Nav.Item>
                            <Nav.Link
                                active={currentStep === 'snapshot'}
                                onClick={() => setCurrentStep('snapshot')}
                            >
                                <PiDatabase /> 1. Requirement Snapshot
                            </Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link
                                active={currentStep === 'manual'}
                                onClick={() => setCurrentStep('manual')}
                                disabled={!wizardData.selectedSnapshot}
                            >
                                <PiFileText /> 2. User Manual
                            </Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link
                                active={currentStep === 'apikey'}
                                onClick={() => setCurrentStep('apikey')}
                                disabled={!wizardData.selectedManual}
                            >
                                <PiKey /> 3. API Key
                            </Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link
                                active={currentStep === 'confirm'}
                                onClick={() => setCurrentStep('confirm')}
                                disabled={!wizardData.selectedAPIKey}
                            >
                                <PiCheck /> 4. Confirm
                            </Nav.Link>
                        </Nav.Item>
                    </Nav>

                    {/* Step Content */}
                    {currentStep === 'snapshot' && (
                        <div>
                            <h5>Select Requirement Snapshot</h5>
                            <p className="text-muted">Choose a completed requirement snapshot to verify against.</p>
                            {snapshotsLoading ? (
                                <div className="text-center py-4">
                                    <div className="spinner-border" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </div>
                                    <p className="mt-2 text-muted">Loading requirement snapshots...</p>
                                </div>
                            ) : (
                                <ListGroup>
                                    {requirementSnapshots.map((snapshot) => (
                                    <ListGroup.Item
                                        key={snapshot.id}
                                        action
                                        active={wizardData.selectedSnapshot?.id === snapshot.id}
                                        onClick={() => setWizardData(prev => ({ ...prev, selectedSnapshot: snapshot }))}
                                    >
                                        <div className="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 className="mb-1">{snapshot.name}</h6>
                                                <p className="mb-1 text-muted">{snapshot.description}</p>
                                                <small>ID: {snapshot.id} | Created: {formatDate(snapshot.createdAt)}</small>
                                            </div>
                                            <Badge bg="success">DONE</Badge>
                                        </div>
                                    </ListGroup.Item>
                                ))}
                                </ListGroup>
                            )}
                        </div>
                    )}

                    {currentStep === 'manual' && (
                        <div>
                            <h5>Select User Manual Preprocessing</h5>
                            <p className="text-muted">Choose a completed user manual preprocessing document.</p>
                            {documentsLoading ? (
                                <div className="text-center py-4">
                                    <div className="spinner-border" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </div>
                                    <p className="mt-2 text-muted">Loading documents...</p>
                                </div>
                            ) : (
                                <ListGroup>
                                    {userManualPreprocessings.map((manual) => (
                                    <ListGroup.Item
                                        key={manual.id}
                                        action
                                        active={wizardData.selectedManual?.id === manual.id}
                                        onClick={() => setWizardData(prev => ({ ...prev, selectedManual: manual }))}
                                    >
                                        <div className="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 className="mb-1">{manual.name}</h6>
                                                <p className="mb-1 text-muted">File: {manual.fileName}</p>
                                                <small>ID: {manual.id} | Created: {formatDate(manual.createdAt)}</small>
                                            </div>
                                            <Badge bg="success">DONE</Badge>
                                        </div>
                                    </ListGroup.Item>
                                ))}
                                </ListGroup>
                            )}
                        </div>
                    )}

                    {currentStep === 'apikey' && (
                        <div>
                            <h5>Select API Key</h5>
                            <p className="text-muted">Choose an API key for the verification process.</p>
                            {apiKeysLoading ? (
                                <div className="text-center py-4">
                                    <div className="spinner-border" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </div>
                                    <p className="mt-2 text-muted">Loading API keys...</p>
                                </div>
                            ) : (
                                <ListGroup>
                                    {apiKeys.filter(key => key.isActive).map((apiKey) => (
                                    <ListGroup.Item
                                        key={apiKey.id}
                                        action
                                        active={wizardData.selectedAPIKey?.id === apiKey.id}
                                        onClick={() => setWizardData(prev => ({ ...prev, selectedAPIKey: apiKey }))}
                                    >
                                        <div className="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 className="mb-1">{apiKey.name}</h6>
                                                <p className="mb-1 text-muted">{apiKey.description}</p>
                                                <small>ID: {apiKey.id}</small>
                                            </div>
                                            <Badge bg={apiKey.type === 'Shared' ? 'primary' : 'info'}>
                                                {apiKey.type}
                                            </Badge>
                                        </div>
                                    </ListGroup.Item>
                                ))}
                                </ListGroup>
                            )}
                        </div>
                    )}

                    {currentStep === 'confirm' && (
                        <div>
                            <h5>Confirm Verification Setup</h5>
                            <p className="text-muted">Review your selections before starting the verification.</p>

                            <Card className="mb-3">
                                <Card.Body>
                                    <h6>Selected Requirement Snapshot:</h6>
                                    {wizardData.selectedSnapshot && (
                                        <div>
                                            <strong>{wizardData.selectedSnapshot.name}</strong>
                                            <br />
                                            <small className="text-muted">ID: {wizardData.selectedSnapshot.id}</small>
                                        </div>
                                    )}
                                </Card.Body>
                            </Card>

                            <Card className="mb-3">
                                <Card.Body>
                                    <h6>Selected User Manual:</h6>
                                    {wizardData.selectedManual && (
                                        <div>
                                            <strong>{wizardData.selectedManual.name}</strong>
                                            <br />
                                            <small className="text-muted">ID: {wizardData.selectedManual.id}</small>
                                        </div>
                                    )}
                                </Card.Body>
                            </Card>

                            <Card className="mb-3">
                                <Card.Body>
                                    <h6>Selected API Key:</h6>
                                    {wizardData.selectedAPIKey && (
                                        <div>
                                            <strong>{wizardData.selectedAPIKey.name}</strong>
                                            <Badge bg={wizardData.selectedAPIKey.type === 'Shared' ? 'primary' : 'info'} className="ms-2">
                                                {wizardData.selectedAPIKey.type}
                                            </Badge>
                                            <br />
                                            <small className="text-muted">ID: {wizardData.selectedAPIKey.id}</small>
                                        </div>
                                    )}
                                </Card.Body>
                            </Card>

                            <Alert variant="info">
                                <strong>Ready to start verification!</strong> This process will analyze your user manual against the selected requirements using the chosen AI model.
                            </Alert>
                        </div>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => { setShowCreateModal(false); resetWizard(); }}>
                        Cancel
                    </Button>
                    {currentStep !== 'snapshot' && (
                        <Button variant="outline-secondary" onClick={handlePrevious}>
                            <PiArrowLeft /> Previous
                        </Button>
                    )}
                    {currentStep !== 'confirm' ? (
                        <Button
                            variant="primary"
                            onClick={handleNext}
                            disabled={!canGoNext()}
                        >
                            Next <PiArrowRight />
                        </Button>
                    ) : (
                        <Button
                            variant="success"
                            onClick={handleStartVerification}
                            disabled={!canGoNext()}
                        >
                            <PiCheck /> Start Verification
                        </Button>
                    )}
                </Modal.Footer>
            </Modal>

            {/* Tag Management Modal */}
            <Modal show={showTagModal} onHide={() => setShowTagModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>Manage Tags</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {selectedVerification && (
                        <>
                            <div className="mb-3">
                                <strong>Verification:</strong> {selectedVerification.id}
                            </div>

                            <div className="mb-3">
                                <h6>Current Tags:</h6>
                                {selectedVerification.tags.length === 0 ? (
                                    <p className="text-muted">No tags assigned</p>
                                ) : (
                                    <div className="d-flex flex-wrap gap-2">
                                        {selectedVerification.tags.map((tag) => (
                                            <div key={tag.id} className="d-flex align-items-center">
                                                {editingTag?.id === tag.id ? (
                                                    <InputGroup size="sm" style={{ width: 'auto' }}>
                                                        <Form.Control
                                                            type="text"
                                                            value={newTagName}
                                                            onChange={(e) => setNewTagName(e.target.value)}
                                                            onKeyDown={(e) => {
                                                                if (e.key === 'Enter') {
                                                                    handleRenameTag(tag.id, newTagName);
                                                                }
                                                            }}
                                                            autoFocus
                                                        />
                                                        <Button
                                                            variant="outline-success"
                                                            size="sm"
                                                            onClick={() => handleRenameTag(tag.id, newTagName)}
                                                        >
                                                            ✓
                                                        </Button>
                                                        <Button
                                                            variant="outline-secondary"
                                                            size="sm"
                                                            onClick={() => setEditingTag(null)}
                                                        >
                                                            ✕
                                                        </Button>
                                                    </InputGroup>
                                                ) : (
                                                    <Badge
                                                        bg="secondary"
                                                        className="d-flex align-items-center gap-1"
                                                    >
                                                        <span
                                                            style={{ cursor: 'pointer' }}
                                                            onClick={() => {
                                                                setEditingTag(tag);
                                                                setNewTagName(tag.name);
                                                            }}
                                                        >
                                                            {tag.name}
                                                        </span>
                                                        <Button
                                                            variant="link"
                                                            size="sm"
                                                            className="p-0 text-white"
                                                            onClick={() => handleDeleteTag(tag.id)}
                                                            style={{ fontSize: '0.8em' }}
                                                        >
                                                            ✕
                                                        </Button>
                                                    </Badge>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>

                            <div className="mb-3">
                                <h6>Add New Tag:</h6>
                                <InputGroup>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter tag name..."
                                        value={newTagName}
                                        onChange={(e) => setNewTagName(e.target.value)}
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter') {
                                                handleAddTag();
                                            }
                                        }}
                                    />
                                    <Button
                                        variant="outline-primary"
                                        onClick={handleAddTag}
                                        disabled={!newTagName.trim()}
                                    >
                                        <PiPlus /> Add
                                    </Button>
                                </InputGroup>
                            </div>
                        </>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowTagModal(false)}>
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Verification Tag Manager Component */}
            <TagManager
                show={showTagManager}
                onHide={() => setShowTagManager(false)}
                title="Verification Tag Manager"
                tags={verificationTagsData?.verificationTags?.items?.filter(tag => tag !== null) || []}
                loading={verificationTagsLoading}
                error={verificationTagsError}
                onCreateTag={handleCreateVerificationTagManager}
                onUpdateTag={handleUpdateVerificationTagManager}
                onDeleteTag={handleDeleteVerificationTagManager}
                createLoading={createVerificationTagLoading}
                updateLoading={renameVerificationTagLoading}
                deleteLoading={deleteVerificationTagLoading}
                createError={createVerificationTagError}
                updateError={renameVerificationTagError}
                deleteError={deleteVerificationTagError}
            />
        </>
    );
}

export default VerificationPage;