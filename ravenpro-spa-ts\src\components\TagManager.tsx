import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
    Offcan<PERSON>,
    Button,
    ListGroup,
    Spinner,
    Alert,
    Modal,
    Form,
    InputGroup
} from 'react-bootstrap';
import { PiTag, PiPlus, PiPencil, PiTrash, PiMagnifyingGlass, PiX } from 'react-icons/pi';

interface Tag {
    id?: string | null;
    text?: string | null;
    createdAt?: string;
}

interface TagManagerProps {
    show: boolean;
    onHide: () => void;
    title?: string;
    tags?: Tag[];
    loading?: boolean;
    error?: Error | null;
    onCreateTag: (text: string) => Promise<void>;
    onUpdateTag: (tag: Tag, newText: string) => Promise<void>;
    onDeleteTag: (tag: Tag) => Promise<void>;
    createLoading?: boolean;
    updateLoading?: boolean;
    deleteLoading?: boolean;
    createError?: Error | null;
    updateError?: Error | null;
    deleteError?: Error | null;
}

const TagManager: React.FC<TagManagerProps> = ({
    show,
    onHide,
    title = "Tag Manager",
    tags = [],
    loading = false,
    error = null,
    onCreateTag,
    onUpdateTag,
    onDeleteTag,
    createLoading = false,
    updateLoading = false,
    deleteLoading = false,
    createError = null,
    updateError = null,
    deleteError = null
}) => {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [selectedTag, setSelectedTag] = useState<Tag | null>(null);
    const [newTagText, setNewTagText] = useState('');
    const [editTagText, setEditTagText] = useState('');
    const [searchText, setSearchText] = useState('');
    const searchInputRef = useRef<HTMLInputElement>(null);

    // Auto-focus search field when tag manager opens
    useEffect(() => {
        if (show && searchInputRef.current) {
            // Small delay to ensure the offcanvas is fully rendered
            setTimeout(() => {
                searchInputRef.current?.focus();
            }, 100);
        }
    }, [show]);

    // Filter tags based on search text
    const filteredTags = useMemo(() => {
        if (!searchText.trim()) {
            return tags;
        }

        const searchLower = searchText.toLowerCase().trim();
        return tags.filter(tag =>
            tag.text?.toLowerCase().includes(searchLower)
        );
    }, [tags, searchText]);

    const handleCreateTag = async () => {
        if (!newTagText.trim()) return;
        
        try {
            await onCreateTag(newTagText.trim());
            setNewTagText('');
            setShowCreateModal(false);
        } catch (error) {
            // Error is handled by parent component
        }
    };

    const handleEditTag = (tag: Tag) => {
        setSelectedTag(tag);
        setEditTagText(tag.text || '');
        setShowEditModal(true);
    };

    const handleUpdateTag = async () => {
        if (!selectedTag || !editTagText.trim()) return;
        
        try {
            await onUpdateTag(selectedTag, editTagText.trim());
            setEditTagText('');
            setSelectedTag(null);
            setShowEditModal(false);
        } catch (error) {
            // Error is handled by parent component
        }
    };

    const handleDeleteTagConfirm = (tag: Tag) => {
        setSelectedTag(tag);
        setShowDeleteModal(true);
    };

    const handleDeleteTag = async () => {
        if (!selectedTag) return;
        
        try {
            await onDeleteTag(selectedTag);
            setSelectedTag(null);
            setShowDeleteModal(false);
        } catch (error) {
            // Error is handled by parent component
        }
    };

    const resetModals = () => {
        setShowCreateModal(false);
        setShowEditModal(false);
        setShowDeleteModal(false);
        setSelectedTag(null);
        setNewTagText('');
        setEditTagText('');
        setSearchText('');
    };

    const handleHide = () => {
        resetModals();
        onHide();
    };

    return (
        <>
            {/* Tag Manager Offcanvas */}
            <Offcanvas show={show} onHide={handleHide} placement="start">
                <Offcanvas.Header closeButton>
                    <Offcanvas.Title>
                        <PiTag className="me-2" />
                        {title}
                    </Offcanvas.Title>
                </Offcanvas.Header>
                <Offcanvas.Body>
                    <div className="d-flex justify-content-between align-items-center mb-3">
                        <h6 className="mb-0">Tags</h6>
                        <Button
                            variant="primary"
                            size="sm"
                            onClick={() => setShowCreateModal(true)}
                            className="d-flex align-items-center gap-1"
                        >
                            <PiPlus /> Create Tag
                        </Button>
                    </div>

                    {/* Search Field */}
                    <div className="mb-3">
                        <InputGroup>
                            <InputGroup.Text>
                                <PiMagnifyingGlass />
                            </InputGroup.Text>
                            <Form.Control
                                ref={searchInputRef}
                                type="text"
                                placeholder="Search tags..."
                                value={searchText}
                                onChange={(e) => setSearchText(e.target.value)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Escape' && searchText) {
                                        setSearchText('');
                                        e.preventDefault();
                                    }
                                }}
                            />
                            {searchText && (
                                <Button
                                    variant="outline-secondary"
                                    onClick={() => setSearchText('')}
                                    title="Clear search"
                                >
                                    <PiX />
                                </Button>
                            )}
                        </InputGroup>
                        {searchText && (
                            <small className="text-muted d-block mt-1">
                                {filteredTags.length === 0 ? (
                                    'No tags match your search'
                                ) : filteredTags.length === tags.length ? (
                                    `All ${tags.length} tags shown`
                                ) : (
                                    `Showing ${filteredTags.length} of ${tags.length} tags`
                                )}
                            </small>
                        )}
                    </div>

                    {loading && (
                        <div className="text-center py-3">
                            <Spinner animation="border" size="sm" />
                            <p className="mt-2 text-muted">Loading tags...</p>
                        </div>
                    )}

                    {error && (
                        <Alert variant="danger">
                            Error loading tags: {error.message}
                        </Alert>
                    )}

                    <ListGroup>
                        {filteredTags.map((tag) => (
                            <ListGroup.Item 
                                key={tag.id} 
                                className="d-flex justify-content-between align-items-center"
                            >
                                <div>
                                    <strong>{tag.text}</strong>
                                    <br />
                                    <small className="text-muted">
                                        Created: {tag.createdAt ? new Date(tag.createdAt).toLocaleDateString() : 'Unknown'}
                                    </small>
                                </div>
                                <div className="d-flex gap-1">
                                    <Button
                                        variant="outline-secondary"
                                        size="sm"
                                        onClick={() => handleEditTag(tag)}
                                        title="Edit tag"
                                    >
                                        <PiPencil />
                                    </Button>
                                    <Button
                                        variant="outline-danger"
                                        size="sm"
                                        onClick={() => handleDeleteTagConfirm(tag)}
                                        title="Delete tag"
                                    >
                                        <PiTrash />
                                    </Button>
                                </div>
                            </ListGroup.Item>
                        ))}
                    </ListGroup>

                    {filteredTags.length === 0 && !loading && (
                        <div className="text-center py-4 text-muted">
                            <PiTag size={48} className="mb-2 opacity-50" />
                            {searchText ? (
                                <div>
                                    <p>No tags match your search "{searchText}"</p>
                                    <Button
                                        variant="link"
                                        size="sm"
                                        onClick={() => setSearchText('')}
                                        className="p-0"
                                    >
                                        Clear search to see all tags
                                    </Button>
                                </div>
                            ) : (
                                <p>No tags found. Create your first tag to get started!</p>
                            )}
                        </div>
                    )}
                </Offcanvas.Body>
            </Offcanvas>

            {/* Create Tag Modal */}
            <Modal show={showCreateModal} onHide={() => setShowCreateModal(false)} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Create New Tag</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form>
                        <Form.Group>
                            <Form.Label>Tag Name</Form.Label>
                            <Form.Control
                                type="text"
                                placeholder="Enter tag name..."
                                value={newTagText}
                                onChange={(e) => setNewTagText(e.target.value)}
                                onKeyDown={(e) => e.key === 'Enter' && handleCreateTag()}
                            />
                        </Form.Group>
                        {createError && (
                            <Alert variant="danger" className="mt-2">
                                Error creating tag: {createError.message}
                            </Alert>
                        )}
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowCreateModal(false)}>
                        Cancel
                    </Button>
                    <Button 
                        variant="primary" 
                        onClick={handleCreateTag}
                        disabled={!newTagText.trim() || createLoading}
                    >
                        {createLoading ? <Spinner animation="border" size="sm" /> : 'Create Tag'}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Edit Tag Modal */}
            <Modal show={showEditModal} onHide={() => setShowEditModal(false)} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Edit Tag</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form>
                        <Form.Group>
                            <Form.Label>Tag Name</Form.Label>
                            <Form.Control
                                type="text"
                                placeholder="Enter tag name..."
                                value={editTagText}
                                onChange={(e) => setEditTagText(e.target.value)}
                                onKeyDown={(e) => e.key === 'Enter' && handleUpdateTag()}
                            />
                        </Form.Group>
                        {updateError && (
                            <Alert variant="danger" className="mt-2">
                                Error updating tag: {updateError.message}
                            </Alert>
                        )}
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowEditModal(false)}>
                        Cancel
                    </Button>
                    <Button 
                        variant="primary" 
                        onClick={handleUpdateTag}
                        disabled={!editTagText.trim() || updateLoading}
                    >
                        {updateLoading ? <Spinner animation="border" size="sm" /> : 'Update Tag'}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Delete Tag Confirmation Modal */}
            <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} centered>
                <Modal.Header closeButton>
                    <Modal.Title>Delete Tag</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <p>Are you sure you want to delete the tag <strong>"{selectedTag?.text}"</strong>?</p>
                    <p className="text-muted">This action cannot be undone and will remove the tag from all associated items.</p>
                    {deleteError && (
                        <Alert variant="danger">
                            Error deleting tag: {deleteError.message}
                        </Alert>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
                        Cancel
                    </Button>
                    <Button 
                        variant="danger" 
                        onClick={handleDeleteTag}
                        disabled={deleteLoading}
                    >
                        {deleteLoading ? <Spinner animation="border" size="sm" /> : 'Delete Tag'}
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
};

export default TagManager;
