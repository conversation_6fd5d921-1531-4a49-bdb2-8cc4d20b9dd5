import { useState, useEffect, useRef, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Con<PERSON>er, Spinner, Card, Badge, ButtonGroup, Stack, Form, InputGroup } from 'react-bootstrap';
import { useGetAccessibleKeysLazyQuery } from '../../gql/graphql';
import type { ApiKey } from '../../gql/graphql';
import { PiArrowsLeftRight, PiCarrot, PiEyeLight, PiEyeSlash, PiFeather, PiKey, PiUploadSimple, PiMagnifyingGlass, PiX } from 'react-icons/pi';
import CreateApiKeyModal from './components/create-api-modal';
import DeleteApiKeyModal from './components/delete-api-modal';
import TransferApiKeyModal from './components/transfer-api-modal';
import RenameApiKeyModal from './components/rename-api-modal';
import ShareApiKeyModal from './components/share-api-modal';


function ApiKeyManagementPage() {

    const OWNER = "<EMAIL>";
    const TOKENLIMIT = 3;

    // State Variables
    const hasLoadedRef = useRef(false);
    const [apiKeysData, setApiKeysData] = useState<ApiKey[]>([]);
    const [nextKey, setNextKey] = useState<string | null>(null);
    const [visibleIds, setVisibleIds] = useState<Record<string, boolean>>({});
    const [selectedApiKey, setSelectedApiKey] = useState<ApiKey>();

    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showShareModal, setShowShareModal] = useState(false);
    const [showTransferModal, setShowTransferModal] = useState(false);
    const [showRenameModal, setShowRenameModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);

    // Search state
    const [searchText, setSearchText] = useState('');
    const searchInputRef = useRef<HTMLInputElement>(null);

    // GraphQL Apollo Functions
    const [fetchKeysData, { loading: keysLoading, error: keysError, data: keysData }] = useGetAccessibleKeysLazyQuery();

    // Filter API keys based on search text
    const filteredApiKeys = useMemo(() => {
        if (!searchText.trim()) {
            return apiKeysData;
        }

        const searchLower = searchText.toLowerCase().trim();
        return apiKeysData.filter(key =>
            key.name?.toLowerCase().includes(searchLower) ||
            key.owner?.toLowerCase().includes(searchLower) ||
            key.status?.toLowerCase().includes(searchLower)
        );
    }, [apiKeysData, searchText]);

    // Helper function to highlight search terms
    const highlightSearchTerm = (text: string, searchTerm: string) => {
        if (!searchTerm.trim()) return text;

        const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        const parts = text.split(regex);

        return parts.map((part, index) =>
            regex.test(part) ? (
                <mark key={index} className="bg-warning bg-opacity-50">{part}</mark>
            ) : part
        );
    };

    const loadKeys = ((token: string | null = null) => {
        fetchKeysData({
            variables: {
                token,
                limit: TOKENLIMIT
            },
            fetchPolicy: 'network-only'
        })
    });

    const handleKeysRefetch = (() => {
        setNextKey(null);
        setApiKeysData([]);
        loadKeys();
    });

    const handleDelete = ((apiKey: ApiKey) => {
        setShowDeleteModal(true);
        setSelectedApiKey(apiKey);
    });

    const handleTransfer = ((apiKey: ApiKey) => {
        setShowTransferModal(true);
        setSelectedApiKey(apiKey);
    });

    const handleRename = ((apiKey: ApiKey) => {
        setShowRenameModal(true);
        setSelectedApiKey(apiKey);
    });

    const handleShare = ((apiKey: ApiKey) => {
        setShowShareModal(true);
        setSelectedApiKey(apiKey);
    }); 

    const toggleIdVisibility = (keyId: string) => {
        setVisibleIds((prev) => ({
            ...prev,
            [keyId]: !prev[keyId],
        }));
    };

    // Initial Load
    useEffect(() => {
        if (!hasLoadedRef.current) {
            hasLoadedRef.current = true;
            loadKeys();
        }
    }, []);

    // Dynamic update of data
    useEffect(() => {
        if (keysData?.accessibleKeys?.items) {
            const newItems = keysData.accessibleKeys.items ?? [];
            const filteredItems = newItems.filter((item): item is ApiKey => item !== null);
            setApiKeysData((prev) => [...prev, ...filteredItems]);
            setNextKey(keysData.accessibleKeys.nextToken ?? null);
        }
    }, [keysData])

    // Keyboard shortcut to focus search field
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
                event.preventDefault();
                searchInputRef.current?.focus();
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, []);

    return (
        <Container className="mt-4">
            <Stack direction="horizontal" gap={2}>
                <div className="p-2">
                    <h1><PiKey></PiKey> API Keys</h1>
                    <p className="text-muted">Manage your API keys, share them with team members, and control access.</p>
                </div>
                <div className="p-2 ms-auto">
                    <Button variant="dark" onClick={() => setShowCreateModal(true)}>+ Create API Key</Button>
                </div>
            </Stack>

            {/* Search Field */}
            <Card className="mb-3">
                <Card.Body>
                    <InputGroup>
                        <InputGroup.Text>
                            <PiMagnifyingGlass />
                        </InputGroup.Text>
                        <Form.Control
                            ref={searchInputRef}
                            type="text"
                            placeholder="Search API keys by name, owner, or status... (Ctrl+F to focus)"
                            value={searchText}
                            onChange={(e) => setSearchText(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === 'Escape' && searchText) {
                                    setSearchText('');
                                    e.preventDefault();
                                }
                            }}
                        />
                        {searchText && (
                            <Button
                                variant="outline-secondary"
                                onClick={() => setSearchText('')}
                                title="Clear search"
                            >
                                <PiX />
                            </Button>
                        )}
                    </InputGroup>
                    {searchText && (
                        <small className="text-muted d-block mt-2">
                            {filteredApiKeys.length === 0 ? (
                                'No API keys match your search'
                            ) : filteredApiKeys.length === apiKeysData.length ? (
                                `All ${apiKeysData.length} API keys shown`
                            ) : (
                                `Showing ${filteredApiKeys.length} of ${apiKeysData.length} API keys`
                            )}
                        </small>
                    )}
                </Card.Body>
            </Card>

            <Card>
                <Card.Body>
                    {keysError ? <Alert variant="danger">Failed to load keys</Alert> : (
                        <Table hover responsive>
                            <thead>
                                <tr>
                                    <th><h5>Available Key(s)</h5></th>
                                </tr>
                            </thead>
                            <tbody>
                                {filteredApiKeys.length === 0 && searchText ? (
                                    <tr>
                                        <td className="text-center text-muted py-4">
                                            <div>
                                                <PiKey size={48} className="mb-2 opacity-50" />
                                                <p>No API keys match your search "{searchText}"</p>
                                                <Button
                                                    variant="link"
                                                    size="sm"
                                                    onClick={() => setSearchText('')}
                                                    className="p-0"
                                                >
                                                    Clear search to see all API keys
                                                </Button>
                                            </div>
                                        </td>
                                    </tr>
                                ) : filteredApiKeys.length === 0 && !searchText ? (
                                    <tr>
                                        <td className="text-center text-muted py-4">
                                            <div>
                                                <PiKey size={48} className="mb-2 opacity-50" />
                                                <p>No API keys found. Create your first API key to get started!</p>
                                            </div>
                                        </td>
                                    </tr>
                                ) : (
                                    filteredApiKeys.map((key) => (
                                    <tr key={key.id}>
                                        {key.owner === OWNER ? (
                                            <>
                                                <td><h5><PiCarrot /> {highlightSearchTerm(key.name || '', searchText)}</h5>
                                                    <Badge bg="dark" pill>OWNED</Badge>{' | '}
                                                    <Badge bg={key.status === "ACTIVE" ? "primary" : "danger"} pill>{highlightSearchTerm(key.status || '', searchText)}</Badge>
                                                    <p>
                                                        {/* <b>Owner: </b>
                                                        {key.owner}
                                                        <br></br>
                                                        <b>Secret: </b>
                                                        {visibleIds[key.id!] ? key.id : '********'}
                                                        <a
                                                            className="m-1 fs-4"
                                                            onClick={() => toggleIdVisibility(key.id!)}
                                                        >
                                                            {visibleIds[key.id!] ? <PiEyeSlash /> : <PiEyeLight />}
                                                        </a> */}
                                                        {/* <br></br> */}
                                                    </p>
                                                    <ButtonGroup size="sm">
                                                        <Button variant="light" onClick={() => handleRename(key)}><PiFeather></PiFeather> Rename</Button>
                                                        <Button variant="light" onClick={() => handleShare(key)}><PiUploadSimple></PiUploadSimple> Share</Button>
                                                        <Button variant="light" onClick={() => handleTransfer(key)}><PiArrowsLeftRight></PiArrowsLeftRight> Transfer</Button>
                                                        <Button variant="danger" onClick={() => handleDelete(key)}>Delete</Button>
                                                    </ButtonGroup>
                                                </td>
                                            </>
                                        ) : (
                                            <>
                                                <td><h5><PiCarrot /> {highlightSearchTerm(key.name || '', searchText)}</h5>
                                                    <Badge bg="light" text="dark" pill>SHARED</Badge>{' | '}
                                                    <Badge bg={key.status === "ACTIVE" ? "primary" : "danger"} pill>{highlightSearchTerm(key.status || '', searchText)}</Badge>
                                                    <p>
                                                        <b>Owner: </b>
                                                        {highlightSearchTerm(key.owner || '', searchText)}
                                                    </p>
                                                </td>
                                            </>
                                        )}
                                    </tr>
                                    ))
                                )}

                            </tbody>
                        </Table>
                    )}
                    <div className="d-flex justify-content-between align-items-center">
                        <Button variant="dark" onClick={() => loadKeys(nextKey)} disabled={keysLoading || nextKey == null || searchText.trim() !== ''}>
                            {keysLoading ? <Spinner animation='border'></Spinner> : nextKey == null ? "No More Data" : "+ Load More"}
                        </Button>
                        {searchText.trim() !== '' && (
                            <small className="text-muted">
                                Load More is disabled while searching. Clear search to load additional API keys.
                            </small>
                        )}
                    </div>
                </Card.Body>
            </Card>
            <CreateApiKeyModal open={showCreateModal} handleKeysRefetch={handleKeysRefetch} onClose={setShowCreateModal} />

            <DeleteApiKeyModal open={showDeleteModal} handleKeysRefetch={handleKeysRefetch} onClose={setShowDeleteModal} apiKey={selectedApiKey!} />

            <TransferApiKeyModal open={showTransferModal} handleKeysRefetch={handleKeysRefetch} onClose={setShowTransferModal} apiKey={selectedApiKey!} />

            <RenameApiKeyModal open={showRenameModal} handleKeysRefetch={handleKeysRefetch} onClose={setShowRenameModal} apiKey={selectedApiKey!} />

            <ShareApiKeyModal open={showShareModal} handleKeysRefetch={handleKeysRefetch} onClose={setShowShareModal} apiKey={selectedApiKey!} />

        </Container>
    )
}

export default ApiKeyManagementPage;
