import { Routes, Route } from 'react-router-dom';
import { LoginCallback } from '@okta/okta-react';
import { RequiredAuth } from './SecureRoute';
import Home from './Home';
import Loading from './Loading';
import ApiKeyManagementPage from '../pages/ApiKeyManagement/page';
import RequirementSnapshotPage from '../pages/RequirementSnapshot/page';
import UserManualPage from '../pages/UserManual/page';
import VerificationPage from '../pages/Verification/page';

const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<Home/>}/>
      <Route path="login/callback" element={<LoginCallback loadingElement={<Loading/>}/>}/>
      <Route path="/apikeymanagement" element={<RequiredAuth/>}>
        <Route path="" element={<ApiKeyManagementPage/>}/>
      </Route>
      <Route path="/requirementsnapshot" element={<RequiredAuth/>}>
        <Route path="" element={<RequirementSnapshotPage/>}/>
      </Route>
      <Route path="/usermanual" element={<RequiredAuth/>}>
        <Route path="" element={<UserManualPage/>}/>
      </Route>
      <Route path="/verification" element={<RequiredAuth/>}>
        <Route path="" element={<VerificationPage/>}/>
      </Route>      
    </Routes>
  );
};

export default AppRoutes;