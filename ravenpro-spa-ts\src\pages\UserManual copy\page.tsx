import { useState, useMemo } from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    But<PERSON>,
    Table,
    Form,
    Modal,
    Badge,
    ProgressBar,
    Row,
    Col,
    InputGroup,
    Alert,
    Card
} from "react-bootstrap";
import { PiNotebook, PiTrash, PiPlus, PiTag, PiCalendar } from "react-icons/pi";
import type { UserManual, UserManualTag, SearchFilters, SearchMode, UserManualStatus } from "../../types/types";
import {
    useGetDocumentsQuery,
    useCreateDocumentMutation,
    useDeleteDocumentMutation,
    DocumentStatusFilter,
    type Document,
    type DocumentStatus
} from "../../gql/graphql";

// Helper function to convert GraphQL Document to UserManual type
const convertDocumentToUserManual = (doc: Document): UserManual => {
    const getStatusFromDocumentStatus = (status?: DocumentStatus | null): UserManualStatus => {
        if (!status) return 'Starting';

        switch (status.__typename) {
            case 'DocumentStatusStarted':
                return 'Starting';
            case 'DocumentStatusUploaded':
            case 'DocumentStatusSplitting':
            case 'DocumentStatusAnnotating':
            case 'DocumentStatusEmbedding':
                return 'Processing';
            case 'DocumentStatusComplete':
                return 'Done';
            case 'DocumentStatusError':
                return 'Error';
            default:
                return 'Starting';
        }
    };

    const getProgressFromDocumentStatus = (status?: DocumentStatus | null): number | undefined => {
        if (!status) return undefined;

        if (status.__typename === 'DocumentStatusAnnotating' || status.__typename === 'DocumentStatusEmbedding') {
            const { current, total } = status;
            return total > 0 ? Math.round((current / total) * 100) : 0;
        }

        return undefined;
    };

    return {
        id: doc.documentId || '',
        author: doc.updatedBy || 'Unknown',
        createdAt: doc.startTime,
        status: getStatusFromDocumentStatus(doc.status),
        tags: [], // TODO: Implement document tags when available in schema
        progress: getProgressFromDocumentStatus(doc.status),
        fileName: `${doc.documentId}-${doc.version}`, // Construct filename from ID and version
        fileSize: undefined // Not available in Document schema
    };
};

// Mock tags for now since document tags are not implemented in the schema yet
const mockTags: UserManualTag[] = [
    { id: "1", name: "Technical" },
    { id: "2", name: "User Guide" },
    { id: "3", name: "API Documentation" },
    { id: "4", name: "Installation" },
    { id: "5", name: "Troubleshooting" }
];

function UserManualPage() {
    // GraphQL hooks
    const { data: documentsData, loading, error, refetch } = useGetDocumentsQuery({
        variables: {
            token: undefined, // Will use pagination token if needed
            limit: 50,
            mineOnly: true,
            status: DocumentStatusFilter.All,
            first: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year ago
            last: new Date().toISOString()
        }
    });

    const [createDocument] = useCreateDocumentMutation();
    const [deleteDocument] = useDeleteDocumentMutation();

    // Convert GraphQL documents to UserManual format
    const userManuals = useMemo(() => {
        if (!documentsData?.documents?.items) return [];
        return documentsData.documents.items
            .filter((doc): doc is Document => doc !== null)
            .map(convertDocumentToUserManual);
    }, [documentsData]);
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showTagModal, setShowTagModal] = useState(false);
    const [selectedManual, setSelectedManual] = useState<UserManual | null>(null);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [searchFilters, setSearchFilters] = useState<SearchFilters>({
        mode: 'mine',
        mineOnly: false,
        statusFilter: [],
        firstDate: '',
        lastDate: ''
    });
    const [tagModalMode, setTagModalMode] = useState<'view' | 'edit'>('view');
    const [editingTag, setEditingTag] = useState<UserManualTag | null>(null);
    const [newTagName, setNewTagName] = useState('');
    const [uploadError, setUploadError] = useState<string | null>(null);

    // Format date from ISO to dd/mm/yyyy-hh:mm
    const formatDate = (isoDate: string): string => {
        const date = new Date(isoDate);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${day}/${month}/${year}-${hours}:${minutes}`;
    };

    // Get status badge variant
    const getStatusVariant = (status: UserManualStatus): string => {
        switch (status) {
            case 'Done': return 'success';
            case 'Processing': return 'primary';
            case 'Starting': return 'info';
            case 'Error': return 'danger';
            case 'Deleting': return 'warning';
            default: return 'secondary';
        }
    };

    // Filter user manuals based on search criteria
    const filteredUserManuals = useMemo(() => {
        return userManuals.filter(manual => {
            // Mode-specific filters
            if (searchFilters.mode === 'mine') {
                if (searchFilters.mineOnly && manual.author !== 'Current User') {
                    return false;
                }
                if (searchFilters.statusFilter && searchFilters.statusFilter.length > 0) {
                    if (!searchFilters.statusFilter.includes(manual.status)) {
                        return false;
                    }
                }
            } else if (searchFilters.mode === 'tags') {
                if (searchFilters.tag) {
                    const hasTag = manual.tags.some(tag =>
                        tag.name.toLowerCase().includes(searchFilters.tag!.toLowerCase())
                    );
                    if (!hasTag) return false;
                }
            }

            // Date filters
            if (searchFilters.firstDate) {
                const manualDate = new Date(manual.createdAt);
                const firstDate = new Date(searchFilters.firstDate);
                if (manualDate < firstDate) return false;
            }

            if (searchFilters.lastDate) {
                const manualDate = new Date(manual.createdAt);
                const lastDate = new Date(searchFilters.lastDate);
                if (manualDate > lastDate) return false;
            }

            return true;
        });
    }, [userManuals, searchFilters]);

    // Handle file upload
    const handleFileUpload = async () => {
        if (!selectedFile) return;

        setUploadError(null);

        try {
            // Generate a unique document ID and version
            const documentId = `doc-${Date.now()}`;
            const version = 'v1.0';

            await createDocument({
                variables: {
                    documentId,
                    version
                }
            });

            // Refetch documents to get the updated list
            await refetch();

            setShowCreateModal(false);
            setSelectedFile(null);
        } catch (error) {
            console.error('Error creating document:', error);
            setUploadError(error instanceof Error ? error.message : 'Failed to upload document');
        }
    };

    // Handle delete manual
    const handleDelete = async (id: string) => {
        if (!window.confirm('Are you sure you want to delete this user manual?')) {
            return;
        }

        try {
            // Find the manual to get the version
            const manual = userManuals.find(m => m.id === id);
            if (!manual) return;

            // Extract version from filename or use default
            const version = 'v1.0'; // TODO: Store version properly when creating documents

            await deleteDocument({
                variables: {
                    documentId: id,
                    version
                }
            });

            // Refetch documents to get the updated list
            await refetch();
        } catch (error) {
            console.error('Error deleting document:', error);
            alert('Failed to delete document. Please try again.');
        }
    };

    // Handle tag operations
    const handleTagEdit = (manual: UserManual) => {
        setSelectedManual(manual);
        setShowTagModal(true);
        setTagModalMode('view');
    };

    const handleAddTag = () => {
        if (!newTagName.trim() || !selectedManual) return;

        const newTag: UserManualTag = {
            id: Date.now().toString(),
            name: newTagName.trim()
        };

        setUserManuals(prev => prev.map(manual =>
            manual.id === selectedManual.id
                ? { ...manual, tags: [...manual.tags, newTag] }
                : manual
        ));

        setNewTagName('');
        setTagModalMode('view');
    };

    const handleDeleteTag = (tagId: string) => {
        if (!selectedManual) return;

        setUserManuals(prev => prev.map(manual =>
            manual.id === selectedManual.id
                ? { ...manual, tags: manual.tags.filter(tag => tag.id !== tagId) }
                : manual
        ));
    };

    const handleRenameTag = (tagId: string, newName: string) => {
        if (!selectedManual || !newName.trim()) return;

        setUserManuals(prev => prev.map(manual =>
            manual.id === selectedManual.id
                ? {
                    ...manual,
                    tags: manual.tags.map(tag =>
                        tag.id === tagId ? { ...tag, name: newName.trim() } : tag
                    )
                }
                : manual
        ));

        setEditingTag(null);
    };

    return (
        <>
            <Container className="mt-4">
                {/* Header */}
                <Stack direction="horizontal" gap={2} className="mb-4">
                    <div className="p-2">
                        <h1><PiNotebook /> User Manual Preprocessing</h1>
                        <p className="text-muted">Upload your user manual to be preprocessed for verification.</p>
                    </div>
                    <div className="p-2 ms-auto">
                        <Button variant="dark" onClick={() => setShowCreateModal(true)}>
                            <PiPlus /> Create New
                        </Button>
                    </div>
                </Stack>

                {/* Error Alert */}
                {error && (
                    <Alert variant="danger" className="mb-4">
                        <strong>Error loading documents:</strong> {error.message}
                        <Button
                            variant="outline-danger"
                            size="sm"
                            className="ms-2"
                            onClick={() => refetch()}
                        >
                            Retry
                        </Button>
                    </Alert>
                )}

                {/* Upload Error Alert */}
                {uploadError && (
                    <Alert variant="danger" className="mb-4" dismissible onClose={() => setUploadError(null)}>
                        <strong>Upload Error:</strong> {uploadError}
                    </Alert>
                )}

                {/* Search Bar */}
                <Card className="mb-4">
                    <Card.Body>
                        <Row className="align-items-end">
                            <Col md={3}>
                                <Form.Label>Search Mode</Form.Label>
                                <Form.Select
                                    value={searchFilters.mode}
                                    onChange={(e) => setSearchFilters(prev => ({
                                        ...prev,
                                        mode: e.target.value as SearchMode,
                                        mineOnly: false,
                                        statusFilter: [],
                                        tag: ''
                                    }))}
                                >
                                    <option value="mine">Mine Only</option>
                                    <option value="tags">Tags</option>
                                </Form.Select>
                            </Col>

                            {searchFilters.mode === 'mine' ? (
                                <>
                                    <Col md={2}>
                                        <Form.Check
                                            type="checkbox"
                                            label="Mine Only"
                                            checked={searchFilters.mineOnly || false}
                                            onChange={(e) => setSearchFilters(prev => ({
                                                ...prev,
                                                mineOnly: e.target.checked
                                            }))}
                                        />
                                    </Col>
                                    <Col md={3}>
                                        <Form.Label>Status Filter</Form.Label>
                                        <Form.Select
                                            multiple
                                            value={searchFilters.statusFilter || []}
                                            onChange={(e) => {
                                                const values = Array.from(e.target.selectedOptions, option => option.value) as UserManualStatus[];
                                                setSearchFilters(prev => ({ ...prev, statusFilter: values }));
                                            }}
                                        >
                                            <option value="Done">Done</option>
                                            <option value="Processing">Processing</option>
                                            <option value="Starting">Starting</option>
                                            <option value="Error">Error</option>
                                            <option value="Deleting">Deleting</option>
                                        </Form.Select>
                                    </Col>
                                </>
                            ) : (
                                <Col md={3}>
                                    <Form.Label>Tag Search</Form.Label>
                                    <InputGroup>
                                        <InputGroup.Text><PiTag /></InputGroup.Text>
                                        <Form.Control
                                            type="text"
                                            placeholder="Search by tag..."
                                            value={searchFilters.tag || ''}
                                            onChange={(e) => setSearchFilters(prev => ({
                                                ...prev,
                                                tag: e.target.value
                                            }))}
                                        />
                                    </InputGroup>
                                </Col>
                            )}

                            <Col md={2}>
                                <Form.Label>First Date</Form.Label>
                                <InputGroup>
                                    <InputGroup.Text><PiCalendar /></InputGroup.Text>
                                    <Form.Control
                                        type="date"
                                        value={searchFilters.firstDate || ''}
                                        onChange={(e) => setSearchFilters(prev => ({
                                            ...prev,
                                            firstDate: e.target.value
                                        }))}
                                    />
                                </InputGroup>
                            </Col>

                            <Col md={2}>
                                <Form.Label>Last Date</Form.Label>
                                <InputGroup>
                                    <InputGroup.Text><PiCalendar /></InputGroup.Text>
                                    <Form.Control
                                        type="date"
                                        value={searchFilters.lastDate || ''}
                                        onChange={(e) => setSearchFilters(prev => ({
                                            ...prev,
                                            lastDate: e.target.value
                                        }))}
                                    />
                                </InputGroup>
                            </Col>
                        </Row>
                    </Card.Body>
                </Card>

                {/* Main Table */}
                <Card>
                    <Card.Body>
                        <Table responsive striped hover>
                            <thead>
                                <tr>
                                    <th>Author</th>
                                    <th>Created At</th>
                                    <th>ID</th>
                                    <th>Status</th>
                                    <th>Tags</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {loading ? (
                                    <tr>
                                        <td colSpan={6} className="text-center text-muted py-4">
                                            Loading documents...
                                        </td>
                                    </tr>
                                ) : filteredUserManuals.length === 0 ? (
                                    <tr>
                                        <td colSpan={6} className="text-center text-muted py-4">
                                            No user manuals found matching your criteria.
                                        </td>
                                    </tr>
                                ) : (
                                    filteredUserManuals.map((manual) => (
                                        <tr key={manual.id}>
                                            <td>{manual.author}</td>
                                            <td>{formatDate(manual.createdAt)}</td>
                                            <td>
                                                <code>{manual.id}</code>
                                            </td>
                                            <td>
                                                <Badge bg={getStatusVariant(manual.status)}>
                                                    {manual.status}
                                                </Badge>
                                                {manual.status === 'Processing' && manual.progress !== undefined && (
                                                    <div className="mt-1">
                                                        <ProgressBar
                                                            now={manual.progress}
                                                            label={`${manual.progress}%`}
                                                            style={{ height: '20px' }}
                                                        />
                                                    </div>
                                                )}
                                            </td>
                                            <td>
                                                <div className="d-flex flex-wrap gap-1">
                                                    {manual.tags.map((tag) => (
                                                        <Badge
                                                            key={tag.id}
                                                            bg="secondary"
                                                            style={{ cursor: 'pointer' }}
                                                            onClick={() => handleTagEdit(manual)}
                                                        >
                                                            {tag.name}
                                                        </Badge>
                                                    ))}
                                                    {manual.tags.length === 0 && (
                                                        <Button
                                                            variant="outline-secondary"
                                                            size="sm"
                                                            onClick={() => handleTagEdit(manual)}
                                                        >
                                                            <PiTag /> Add Tags
                                                        </Button>
                                                    )}
                                                </div>
                                            </td>
                                            <td>
                                                <Button
                                                    variant="outline-danger"
                                                    size="sm"
                                                    onClick={() => handleDelete(manual.id)}
                                                    disabled={manual.status === 'Deleting'}
                                                >
                                                    <PiTrash />
                                                </Button>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </Table>
                    </Card.Body>
                </Card>
            </Container>

            {/* Create New Modal */}
            <Modal show={showCreateModal} onHide={() => setShowCreateModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>Upload New User Manual</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Form>
                        <Form.Group className="mb-3">
                            <Form.Label>Select File</Form.Label>
                            <Form.Control
                                type="file"
                                accept=".pdf,.doc,.docx,.txt"
                                onChange={(e) => {
                                    const target = e.target as HTMLInputElement;
                                    if (target.files && target.files[0]) {
                                        setSelectedFile(target.files[0]);
                                    }
                                }}
                            />
                            <Form.Text className="text-muted">
                                Accepted formats: PDF, Word (.doc, .docx), and Text (.txt) files
                            </Form.Text>
                        </Form.Group>

                        {selectedFile && (
                            <Alert variant="info">
                                <strong>Selected file:</strong> {selectedFile.name}
                                ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                            </Alert>
                        )}
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowCreateModal(false)}>
                        Cancel
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleFileUpload}
                        disabled={!selectedFile}
                    >
                        Upload
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Tag Management Modal */}
            <Modal show={showTagModal} onHide={() => setShowTagModal(false)} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>Manage Tags</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {selectedManual && (
                        <>
                            <div className="mb-3">
                                <strong>User Manual:</strong> {selectedManual.fileName || `ID: ${selectedManual.id}`}
                            </div>

                            <div className="mb-3">
                                <h6>Current Tags:</h6>
                                {selectedManual.tags.length === 0 ? (
                                    <p className="text-muted">No tags assigned</p>
                                ) : (
                                    <div className="d-flex flex-wrap gap-2">
                                        {selectedManual.tags.map((tag) => (
                                            <div key={tag.id} className="d-flex align-items-center">
                                                {editingTag?.id === tag.id ? (
                                                    <InputGroup size="sm" style={{ width: 'auto' }}>
                                                        <Form.Control
                                                            type="text"
                                                            value={newTagName}
                                                            onChange={(e) => setNewTagName(e.target.value)}
                                                            onKeyDown={(e) => {
                                                                if (e.key === 'Enter') {
                                                                    handleRenameTag(tag.id, newTagName);
                                                                }
                                                            }}
                                                            autoFocus
                                                        />
                                                        <Button
                                                            variant="outline-success"
                                                            size="sm"
                                                            onClick={() => handleRenameTag(tag.id, newTagName)}
                                                        >
                                                            ✓
                                                        </Button>
                                                        <Button
                                                            variant="outline-secondary"
                                                            size="sm"
                                                            onClick={() => setEditingTag(null)}
                                                        >
                                                            ✕
                                                        </Button>
                                                    </InputGroup>
                                                ) : (
                                                    <Badge
                                                        bg="secondary"
                                                        className="d-flex align-items-center gap-1"
                                                    >
                                                        <span
                                                            style={{ cursor: 'pointer' }}
                                                            onClick={() => {
                                                                setEditingTag(tag);
                                                                setNewTagName(tag.name);
                                                            }}
                                                        >
                                                            {tag.name}
                                                        </span>
                                                        <Button
                                                            variant="link"
                                                            size="sm"
                                                            className="p-0 text-white"
                                                            onClick={() => handleDeleteTag(tag.id)}
                                                            style={{ fontSize: '0.8em' }}
                                                        >
                                                            ✕
                                                        </Button>
                                                    </Badge>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>

                            <div className="mb-3">
                                <h6>Add New Tag:</h6>
                                <InputGroup>
                                    <Form.Control
                                        type="text"
                                        placeholder="Enter tag name..."
                                        value={newTagName}
                                        onChange={(e) => setNewTagName(e.target.value)}
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter') {
                                                handleAddTag();
                                            }
                                        }}
                                    />
                                    <Button
                                        variant="outline-primary"
                                        onClick={handleAddTag}
                                        disabled={!newTagName.trim()}
                                    >
                                        <PiPlus /> Add
                                    </Button>
                                </InputGroup>
                            </div>
                        </>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={() => setShowTagModal(false)}>
                        Close
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
}

export default UserManualPage;