import websocket
import json
import uuid
import time
import threading

WS_ENDPOINT = "wss://frkkgud75zgnhkiopi2zuvzkya.appsync-realtime-api.ap-southeast-2.amazonaws.com"
OKTA_JWT = ""

SUBSCRIPTION_QUERY = """
subscription OnMessage {
  onMessage {
    content
    sender
  }
}
"""

HOST = WS_ENDPOINT.replace("wss://", "").split("/")[0]

def build_connection_init():
    return {
        "type": "connection_init",
        "payload": {
            "authorization": OKTA_JWT,
            "host": HOST
        }
    }

def build_start_msg(sub_id):
    return {
        "id": sub_id,
        "type": "start",
        "payload": {
            "data": json.dumps({
                "query": SUBSCRIPTION_QUERY,
                "variables": {}
            }),
            "extensions": {
                "authorization": {
                    "authorization": OKTA_JWT,
                    "host": HOST
                }
            }
        }
    }

def build_stop_msg(sub_id):
    return {
        "id": sub_id,
        "type": "stop"
    }

# WebSocket handlers
def on_message(ws, message):
    print("Message received:")
    print(json.dumps(json.loads(message), indent=2))

def on_error(ws, error):
    print("WebSocket error:", error)

def on_close(ws, code, msg):
    print(f"WebSocket closed (code={code}): {msg}")

def on_open(ws):
    print("WebSocket connection opened")

    # Send connection_init
    init_payload = build_connection_init()
    print("Sending connection_init")
    ws.send(json.dumps(init_payload))

    time.sleep(1)  # small wait to ensure init is acknowledged

    # Send start message to subscribe
    sub_id = str(uuid.uuid4())
    start_payload = build_start_msg(sub_id)
    print("Sending subscription start")
    ws.send(json.dumps(start_payload))

# Run WebSocket connection
def run_ws():
    websocket.enableTrace(False)
    ws = websocket.WebSocketApp(
        WS_ENDPOINT,
        subprotocols=["graphql-ws"],  
        on_open=on_open,
        on_message=on_message,
        on_error=on_error,
        on_close=on_close
    )
    ws.run_forever()

# Start in thread
threading.Thread(target=run_ws).start()