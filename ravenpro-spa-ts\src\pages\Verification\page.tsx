import { useState, useMemo, useRef, useEffect } from "react";
import {
    <PERSON>tainer,
    <PERSON>ack,
    Button,
    Table,
    Form,
    Modal,
    Badge,
    ProgressBar,
    Row,
    Col,
    InputGroup,
    Alert,
    Card,
    ListGroup,
    Nav,
    Breadcrumb,
    Tab,
    Tabs,
    Spinner
} from "react-bootstrap";
import {
    PiSealCheck,
    PiTrash,
    PiPlus,
    PiTag,
    PiCalendar,
    PiArrowLeft,
    PiArrowRight,
    PiCheck,
    PiFileText,
    PiKey,
    PiDatabase,
    PiThreeD,
    PiMagnifyingGlass,
    PiCaretCircleDoubleRight,
    PiCaretDoubleRight,
    PiDownload
} from "react-icons/pi";
import type {
    Verification,
    VerificationTag,
    VerificationSearchFilters,
    VerificationSearchMode,
    VerificationStatus,
    ProcessingStatus,
    RequirementSnapshot,
    UserManualPreprocessing,
    APIKey,
    VerificationWizardStep,
    VerificationWizardData
} from "../../types/types";
import {
    useGetVerificationsQuery,
    useCreateVerificationMutation,
    useDeleteVerificationMutation,
    useGetRequirementSnapshotsQuery,
    useGetDocumentsQuery,
    useGetOwnedKeysQuery,
    useGetVerificationTagsLazyQuery,
    useCreateVerificationTagMutation,
    useRenameVerificationTagMutation,
    useDeleteVerificationTagMutation,
    VerificationStatusFilter,
    RequirementSnapshotStatusFilter,
    DocumentStatusFilter,
    type Verification as GraphQLVerification,
    type RequirementSnapshot as GraphQLRequirementSnapshot,
    type Document as GraphQLDocument,
    type ApiKey as GraphQLApiKey,
    useGetVerificationLazyQuery,
    useGetVerificationsLazyQuery,
    useGetVerificationsWithTagLazyQuery
} from "../../gql/graphql";
import TagManager from "../../components/TagManager";
import { StatusPill } from "./components/status-pill";

type verificationFilter = {
    mineOnly?: boolean;
    first?: string;
    last?: string;
    status?: VerificationStatusFilter;
    tag?: string;
}

function VerificationPage() {

    const TOKENLIMIT = 20;
    const TOKENLIMITMAX = 1000;
    const hasLoadedRef = useRef(false);

    // Local state
    const [searchKey, setSearchKey] = useState<string>('status');
    const [searchDisplay, setSearchDisplay] = useState<string>("status");
    const [filter, setFilter] = useState<verificationFilter>({
        mineOnly: true,
        last: new Date().toISOString(),
        first: new Date(new Date().setMonth(new Date().getMonth() - 6)).toISOString(),
        status: VerificationStatusFilter.All,
        tag: ''
    });

    const [selectedEntry, setSelectedEntry] = useState<Verification>();
    const [showCreateModal, setShowCreateModal] = useState<boolean>(false);
    const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
    const [showTagManager, setShowTagManager] = useState<boolean>(false);
    const [showTagVerificationModal, setShowTagVerificationModal] = useState<boolean>(false);


    // GraphQL hooks
    const [fetchVerifications, { loading: verificationsLoading, error: verificationsError, data: verificationsData, refetch: verificationsRefetch }] = useGetVerificationsLazyQuery();
    const [fetchVerificationsWTag, { loading: verificationsWTagLoading, error: verificationsWTagError, data: verificationsWTagData, refetch: verificationsWTagRefetch }] = useGetVerificationsWithTagLazyQuery();


    // Verification Tag Management
    const [fetchVerificationTags, { loading: verificationTagsLoading, error: verificationTagsError, data: verificationTagsData }] = useGetVerificationTagsLazyQuery();
    const [createVerificationTag, { loading: createVerificationTagLoading, error: createVerificationTagError }] = useCreateVerificationTagMutation();
    const [renameVerificationTag, { loading: renameVerificationTagLoading, error: renameVerificationTagError }] = useRenameVerificationTagMutation();
    const [deleteVerificationTag, { loading: deleteVerificationTagLoading, error: deleteVerificationTagError }] = useDeleteVerificationTagMutation();

    // const [editingTag, setEditingTag] = useState<VerificationTag | null>(null);
    // const [newTagName, setNewTagName] = useState('');

    // Wizard state
    const [currentStep, setCurrentStep] = useState<VerificationWizardStep>('snapshot');
    const [wizardData, setWizardData] = useState<VerificationWizardData>({});

    // Load verification data
    const loadVerifications = ((token: string | null = null) => {
        fetchVerifications({
            variables: {
                token,
                limit: TOKENLIMITMAX,
                mineOnly: filter.mineOnly!,
                first: filter.first,
                last: filter.last,
                statusFilter: filter.status!
            },
            fetchPolicy: "network-only"
        })
    });

    const loadVerificationsWTag = ((token: string | null = null) => {
        fetchVerificationsWTag({
            variables: {
                token,
                limit: TOKENLIMITMAX,
                first: filter.first,
                last: filter.last,
                tag: filter.tag
            },
            fetchPolicy: "network-only"
        })
    });

    // Load verification tags
    const loadVerificationTags = (token: | string | null = null) => {
        fetchVerificationTags({
            variables: {
                token: token,
                limit: TOKENLIMITMAX,
                prefix: ""
            },
            fetchPolicy: 'network-only'
        });
    };

    // Initial Load
    useEffect(()=>{
        loadVerifications();
        loadVerificationTags();
    }, [])

    // Tag Management Handlers for TagManager component
    const handleCreateVerificationTagManager = async (text: string) => {
        await createVerificationTag({
            variables: { text }
        });
        loadVerificationTags(); // Refresh tags list
    };

    const handleUpdateVerificationTagManager = async (tag: any, newText: string) => {
        await renameVerificationTag({
            variables: {
                tagId: tag.id,
                oldText: tag.text,
                newText
            }
        });
        loadVerificationTags(); // Refresh tags list
    };

    const handleDeleteVerificationTagManager = async (tag: any) => {
        await deleteVerificationTag({
            variables: {
                id: tag.id,
                text: tag.text
            }
        });
        loadVerificationTags(); // Refresh tags list
    };

    // Utilities
    function toReadableLocalTime(isoString: string): string {
        const date = new Date(isoString);

        // Format date as DD/MM/YYYY
        const datePart = date.toLocaleDateString(undefined, {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
        });

        // Format time as hh:mm am/pm
        const timePart = date.toLocaleTimeString(undefined, {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
        }).toLowerCase();

        return `${datePart} ${timePart}`;
    }

    // Button Funcs
    const handleSearch = (() => {
        if (searchKey === "status") {
            loadVerifications();
            setSearchDisplay("status");
        } else {
            loadVerificationsWTag();
            setSearchDisplay("tag");
        }
    });

    const handleDelete = ((verification: Verification) => {
        setSelectedEntry(verification);
        setShowDeleteModal(true);
    });

    // Wizard navigation
    const getStepIndex = (step: VerificationWizardStep): number => {
        const steps: VerificationWizardStep[] = ['snapshot', 'manual', 'apikey', 'confirm'];
        return steps.indexOf(step);
    };

    const canGoNext = (): boolean => {
        switch (currentStep) {
            case 'snapshot': return !!wizardData.selectedSnapshot;
            case 'manual': return !!wizardData.selectedManual;
            case 'apikey': return !!wizardData.selectedAPIKey;
            case 'confirm': return true;
            default: return false;
        }
    };

    const handleNext = () => {
        const steps: VerificationWizardStep[] = ['snapshot', 'manual', 'apikey', 'confirm'];
        const currentIndex = getStepIndex(currentStep);
        if (currentIndex < steps.length - 1) {
            setCurrentStep(steps[currentIndex + 1]);
        }
    };

    const handlePrevious = () => {
        const steps: VerificationWizardStep[] = ['snapshot', 'manual', 'apikey', 'confirm'];
        const currentIndex = getStepIndex(currentStep);
        if (currentIndex > 0) {
            setCurrentStep(steps[currentIndex - 1]);
        }
    };

    // const handleStartVerification = async () => {
    //     if (!wizardData.selectedSnapshot || !wizardData.selectedManual || !wizardData.selectedAPIKey) {
    //         return;
    //     }

    //     try {
    //         await createVerification({
    //             variables: {
    //                 documentId: wizardData.selectedManual.id,
    //                 documentVersion: 'v1.0',
    //                 snapshotId: wizardData.selectedSnapshot.id
    //             }
    //         });

    //         // Refetch verifications to get the updated list
    //         await refetchVerifications();

    //         setShowCreateModal(false);
    //         setCurrentStep('snapshot');
    //         setWizardData({});
    //     } catch (error) {
    //         console.error('Error creating verification:', error);
    //         alert('Failed to create verification. Please try again.');
    //     }
    // };

    const resetWizard = () => {
        setCurrentStep('snapshot');
        setWizardData({});
    };

    return (
        <>
            <Container className="mt-4">
                {/* Header */}
                <Breadcrumb>
                    <Breadcrumb.Item href="/">Home</Breadcrumb.Item>
                    <Breadcrumb.Item active>Verification</Breadcrumb.Item>
                </Breadcrumb>
                <Stack direction="horizontal" gap={2}>
                    <div className="me-auto">
                        <h3><PiSealCheck /> Verification</h3>
                        <p className="text-muted">Use GenAI to verify your user manual against the JAMA requirements.</p>
                    </div>
                    <div className="ms-auto">
                        <Button
                            variant="outline-primary"
                            onClick={() => setShowTagManager(true)}
                            className="m-2"
                        >
                            <PiTag /> Tag Manager
                        </Button>
                        <Button variant="dark" onClick={() => setShowCreateModal(true)}>
                            <PiPlus /> New
                        </Button>
                    </div>
                </Stack>

                {/* Search Bar */}
                <Card className="mb-3">
                    <Card.Body>
                        <Row>
                            <Col md={8}>
                                <Tabs id="search-panel" activeKey={searchKey} onSelect={(k) => setSearchKey(k!)} className="mb-3">
                                    <Tab eventKey="Search" title="Search By: " disabled />
                                    <Tab eventKey="status" title="Status">
                                        <InputGroup className="p-2">
                                            <InputGroup.Text>Status</InputGroup.Text>
                                            <Form.Select
                                                value={filter.status}
                                                onChange={(e) =>
                                                    setFilter(prev => ({
                                                        ...prev,
                                                        status: e.target.value as VerificationStatusFilter
                                                    }))
                                                }>
                                                {Object.values(VerificationStatusFilter).map((status) => (
                                                    <option key={status} value={status}>
                                                        {status}
                                                    </option>
                                                ))}
                                            </Form.Select>
                                            <InputGroup.Text>Mine Only</InputGroup.Text>
                                            <InputGroup.Text>
                                                <Form.Check type="checkbox"
                                                    checked={filter.mineOnly}
                                                    onChange={(e) =>
                                                        setFilter(prev => ({
                                                            ...prev,
                                                            mineOnly: e.target.checked
                                                        }))} />
                                            </InputGroup.Text>
                                        </InputGroup>
                                    </Tab>
                                    <Tab eventKey="tag" title="Tag">
                                        <InputGroup className="p-2">
                                            <InputGroup.Text>Tag</InputGroup.Text>
                                            <Form.Control type="text">
                                            </Form.Control>
                                        </InputGroup>
                                    </Tab>
                                </Tabs>
                            </Col>
                            <Col md={4}>
                                <InputGroup className="p-2">
                                    <InputGroup.Text>From</InputGroup.Text>
                                    <Form.Control
                                        type="date"
                                        value={filter.first!.slice(0, 10)}
                                        onKeyDown={(e) => e.preventDefault()}
                                        onChange={(e) =>
                                            setFilter(prev => ({
                                                ...prev,
                                                first: new Date(e.target.value).toISOString() || ""
                                            }))} />
                                </InputGroup>
                                <InputGroup className="p-2">
                                    <InputGroup.Text>To</InputGroup.Text>
                                    <Form.Control
                                        type="date"
                                        value={filter.last!.slice(0, 10)}
                                        onKeyDown={(e) => e.preventDefault()}
                                        onChange={(e) =>
                                            setFilter(prev => ({
                                                ...prev,
                                                last: new Date(e.target.value).toISOString() || ""
                                            }))} />
                                </InputGroup>
                            </Col>
                        </Row>
                        <div className="d-flex">
                            <Button variant="outline-dark" className="ms-auto" onClick={handleSearch}>Search</Button>
                        </div>
                    </Card.Body>
                </Card>

                {/* Main Table */}
                <Card>
                    <Card.Body>
<Row>
                        <Col>
                            <InputGroup className="mb-4">
                                <InputGroup.Text><PiMagnifyingGlass></PiMagnifyingGlass></InputGroup.Text>
                                <Form.Control type="text" placeholder="Filter By.. ">
                                    
                                </Form.Control>
                            </InputGroup>
                        </Col>
                    </Row>

                    <Table hover responsive bordered className="small-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Author</th>
                                <th>Document</th>
                                <th>Snapshot</th>
                                <th>Created</th>
                                <th>Status</th>
                                <th>Report</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {verificationsLoading ?
                                <tr><td colSpan={7}><Spinner animation="border" /></td></tr>
                                : verificationsError ?
                                    <tr><td colSpan={7}><Alert variant="danger">{verificationsError.message}</Alert></td></tr>
                                    : verificationsData?.verifications?.items ?
                                        <>
                                            {verificationsData.verifications.items.map((key) => (
                                                <tr key={key?.id}>
                                                    <td>{key?.id}</td>
                                                    <td>{key?.createdBy}</td>
                                                    <td>{key?.documentId}<br/>{key?.documentVersion}</td>
                                                    <td>{key?.snapshotId}</td>
                                                    <td>{toReadableLocalTime(key?.createdAt)}</td>
                                                    <td><StatusPill status={key?.status!} /></td>
                                                    <td><Button className="m-1" variant="outline-primary" size="sm"><PiDownload></PiDownload></Button></td>
                                                    <td>
                                                        <Button className="m-1" variant="outline-danger" size="sm"><PiTrash></PiTrash></Button>
                                                        <Button variant="outline-primary" size="sm" className="m-1"><PiCaretDoubleRight></PiCaretDoubleRight></Button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </> :
                                        <>
                                            <tr><td colSpan={7}><Alert variant="light">Data not retrieved</Alert></td></tr>
                                        </>
                            }
                        </tbody>
                    </Table>
                    </Card.Body>
                </Card>



                {/* Tag Manager Component */}
                <TagManager
                    show={showTagManager}
                    onHide={() => setShowTagManager(false)}
                    title="Verification Tag Manager"
                    tags={verificationTagsData?.verificationTags?.items?.filter(tag => tag !== null) || []}
                    loading={verificationTagsLoading}
                    error={verificationTagsError}
                    onCreateTag={handleCreateVerificationTagManager}
                    onUpdateTag={handleUpdateVerificationTagManager}
                    onDeleteTag={handleDeleteVerificationTagManager}
                    createLoading={createVerificationTagLoading}
                    updateLoading={renameVerificationTagLoading}
                    deleteLoading={deleteVerificationTagLoading}
                    createError={createVerificationTagError}
                    updateError={renameVerificationTagError}
                    deleteError={deleteVerificationTagError}
                />
            </Container>


        </>
    );
}

export default VerificationPage;